// DIAGNOSTIC TERRAIN - Analyse des problèmes de terrain et physique
console.log('🌍 DIAGNOSTIC TERRAIN - Démarrage...');

class TerrainDiagnostic {
    constructor() {
        this.issues = {
            surfaceHoles: 0,
            playerBounces: 0,
            waterGeneration: 0,
            physicsInstability: 0
        };
        
        this.lastPlayerPosition = null;
        this.bounceDetectionThreshold = 0.5;
        this.positionHistory = [];
        this.maxHistoryLength = 60; // 1 seconde à 60 FPS
        
        this.startDiagnostic();
    }
    
    startDiagnostic() {
        console.log('🔍 Démarrage du diagnostic terrain...');
        
        // Attendre que les objets soient disponibles
        this.waitForObjects().then(() => {
            this.initializeMonitoring();
        });
    }
    
    waitForObjects() {
        return new Promise((resolve) => {
            const checkObjects = () => {
                if (window.player && window.world) {
                    resolve();
                } else {
                    setTimeout(checkObjects, 100);
                }
            };
            checkObjects();
        });
    }
    
    initializeMonitoring() {
        console.log('✅ Objets détectés, initialisation du monitoring...');
        
        // Surveiller la position du joueur
        this.monitorPlayerMovement();
        
        // Analyser le terrain autour du joueur
        this.analyzeTerrain();
        
        // Surveiller la génération d'eau
        this.monitorWaterGeneration();
        
        console.log('📊 Monitoring terrain activé');
    }
    
    monitorPlayerMovement() {
        const checkMovement = () => {
            if (!window.player || !window.player.camera) {
                requestAnimationFrame(checkMovement);
                return;
            }
            
            const currentPos = {
                x: window.player.camera.position.x,
                y: window.player.camera.position.y,
                z: window.player.camera.position.z,
                onGround: window.player.onGround,
                velocity: {
                    x: window.player.velocity.x,
                    y: window.player.velocity.y,
                    z: window.player.velocity.z
                },
                timestamp: Date.now()
            };
            
            // Ajouter à l'historique
            this.positionHistory.push(currentPos);
            if (this.positionHistory.length > this.maxHistoryLength) {
                this.positionHistory.shift();
            }
            
            // Détecter les rebonds
            this.detectBounces();
            
            // Détecter l'instabilité physique
            this.detectPhysicsInstability();
            
            this.lastPlayerPosition = currentPos;
            requestAnimationFrame(checkMovement);
        };
        
        requestAnimationFrame(checkMovement);
    }
    
    detectBounces() {
        if (this.positionHistory.length < 10) return;
        
        const recent = this.positionHistory.slice(-10);
        let bounceCount = 0;
        
        for (let i = 1; i < recent.length - 1; i++) {
            const prev = recent[i - 1];
            const curr = recent[i];
            const next = recent[i + 1];
            
            // Détecter un rebond : descente puis montée rapide
            if (prev.velocity.y < -5 && curr.velocity.y > 5 && curr.onGround) {
                bounceCount++;
            }
        }
        
        if (bounceCount > 2) {
            this.issues.playerBounces++;
            console.warn('🦘 REBOND DÉTECTÉ:', {
                bounceCount: bounceCount,
                position: this.lastPlayerPosition,
                recentVelocities: recent.map(p => p.velocity.y)
            });
        }
    }
    
    detectPhysicsInstability() {
        if (this.positionHistory.length < 30) return;
        
        const recent = this.positionHistory.slice(-30);
        let rapidChanges = 0;
        
        for (let i = 1; i < recent.length; i++) {
            const prev = recent[i - 1];
            const curr = recent[i];
            
            // Changement rapide d'état onGround
            if (prev.onGround !== curr.onGround) {
                rapidChanges++;
            }
        }
        
        if (rapidChanges > 10) {
            this.issues.physicsInstability++;
            console.warn('⚡ INSTABILITÉ PHYSIQUE DÉTECTÉE:', {
                rapidChanges: rapidChanges,
                timeWindow: '0.5 secondes',
                position: this.lastPlayerPosition
            });
        }
    }
    
    analyzeTerrain() {
        if (!window.player || !window.world) return;
        
        const playerPos = window.player.camera.position;
        const radius = 10; // Analyser dans un rayon de 10 blocs
        
        let holesDetected = 0;
        let waterBlocksDetected = 0;
        
        for (let x = playerPos.x - radius; x <= playerPos.x + radius; x += 2) {
            for (let z = playerPos.z - radius; z <= playerPos.z + radius; z += 2) {
                // Vérifier les trous en surface
                const groundHeight = window.world.getGroundHeightAt(x, z);
                if (groundHeight !== null) {
                    // Vérifier s'il y a des trous dans les 5 blocs sous la surface
                    for (let y = groundHeight - 1; y >= groundHeight - 5; y--) {
                        const blockType = window.world.getBlockTypeAt(x, y, z);
                        if (blockType === 0) { // Air
                            holesDetected++;
                        }
                    }
                    
                    // Vérifier la génération d'eau inappropriée
                    for (let y = groundHeight; y <= groundHeight + 10; y++) {
                        const blockType = window.world.getBlockTypeAt(x, y, z);
                        if (blockType === 9) { // Eau (BLOCK_TYPES.WATER)
                            waterBlocksDetected++;
                        }
                    }
                }
            }
        }
        
        if (holesDetected > 5) {
            this.issues.surfaceHoles++;
            console.warn('🕳️ TROUS EN SURFACE DÉTECTÉS:', {
                holesCount: holesDetected,
                playerPosition: playerPos,
                radius: radius
            });
        }
        
        if (waterBlocksDetected > 0) {
            this.issues.waterGeneration++;
            console.warn('💧 GÉNÉRATION D\'EAU INAPPROPRIÉE:', {
                waterBlocks: waterBlocksDetected,
                playerPosition: playerPos,
                radius: radius
            });
        }
        
        // Répéter l'analyse toutes les 5 secondes
        setTimeout(() => this.analyzeTerrain(), 5000);
    }
    
    monitorWaterGeneration() {
        // Intercepter les logs de génération de chunks si disponible
        if (window.GameLogger && window.GameLogger.chunk) {
            const originalChunk = window.GameLogger.chunk;
            
            window.GameLogger.chunk = (message, data) => {
                originalChunk.call(window.GameLogger, message, data);
                
                if (message.includes('généré') && data && data.blocksCount) {
                    // Analyser la génération d'eau dans ce chunk
                    this.analyzeChunkWaterGeneration(data);
                }
            };
        }
    }
    
    analyzeChunkWaterGeneration(chunkData) {
        // Cette méthode pourrait être étendue pour analyser
        // la génération d'eau dans les chunks nouvellement créés
        console.log('🌊 Analyse génération d\'eau pour chunk:', chunkData);
    }
    
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            issues: this.issues,
            playerStatus: this.lastPlayerPosition ? {
                position: this.lastPlayerPosition,
                onGround: this.lastPlayerPosition.onGround,
                velocity: this.lastPlayerPosition.velocity
            } : null,
            recommendations: this.getRecommendations()
        };
        
        console.log('📋 RAPPORT DIAGNOSTIC TERRAIN:', report);
        return report;
    }
    
    getRecommendations() {
        const recommendations = [];
        
        if (this.issues.surfaceHoles > 0) {
            recommendations.push('Vérifier la génération de grottes - trop proches de la surface');
            recommendations.push('Augmenter la protection contre les trous (minimum 8 blocs sous surface)');
        }
        
        if (this.issues.playerBounces > 0) {
            recommendations.push('Améliorer la prédiction d\'atterrissage du joueur');
            recommendations.push('Réduire la sensibilité de détection du sol');
        }
        
        if (this.issues.waterGeneration > 0) {
            recommendations.push('Limiter la génération d\'eau aux océans profonds uniquement');
            recommendations.push('Vérifier les conditions de biome pour l\'eau');
        }
        
        if (this.issues.physicsInstability > 0) {
            recommendations.push('Stabiliser la physique du joueur');
            recommendations.push('Implémenter un système d\'amortissement');
        }
        
        if (recommendations.length === 0) {
            recommendations.push('Aucun problème majeur détecté - terrain stable');
        }
        
        return recommendations;
    }
}

// Initialiser le diagnostic automatiquement
let terrainDiagnostic;

function initTerrainDiagnostic() {
    terrainDiagnostic = new TerrainDiagnostic();
    
    // Fonction globale pour générer un rapport
    window.generateTerrainReport = () => {
        if (terrainDiagnostic) {
            return terrainDiagnostic.generateReport();
        } else {
            console.warn('Diagnostic terrain non initialisé');
            return null;
        }
    };
    
    console.log('🌍 Diagnostic terrain prêt. Utilisez generateTerrainReport() pour un rapport.');
}

// Démarrer le diagnostic
window.addEventListener('load', () => {
    setTimeout(initTerrainDiagnostic, 2000);
});

// Export pour utilisation manuelle
window.initTerrainDiagnostic = initTerrainDiagnostic;
