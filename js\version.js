// ===== SYSTÈME DE VERSIONING ET CACHE-BUSTING CENTRALISÉ =====
// Ce fichier est automatiquement mis à jour à chaque modification

export const VERSION_CONFIG = {
    // Version principale du jeu
    GAME_VERSION: `v${Date.now()}`,
    
    // Cache buster unique pour cette session
    CACHE_BUSTER: Date.now(),
    
    // Versions des modules individuels
    MODULES: {
        PLAYER: `Player-v${Date.now()}`,
        WORLD: `World-v${Date.now()}`,
        CHUNK: `Chunk-v${Date.now()}`,
        CONTROLS: `Controls-v${Date.now()}`,
        MAIN: `Main-v${Date.now()}`
    },
    
    // Timestamp de dernière modification
    LAST_UPDATED: new Date().toISOString(),
    
    // Méthode pour afficher les informations de version
    displayVersionInfo() {
        console.log(`🚀 ===== MINECRAFT JS - ${this.GAME_VERSION} =====`);
        console.log(`📅 Dernière mise à jour: ${this.LAST_UPDATED}`);
        console.log(`🔄 Cache Buster: ${this.CACHE_BUSTER}`);
        console.log(`📦 Modules chargés:`);
        Object.entries(this.MODULES).forEach(([name, version]) => {
            console.log(`   ✅ ${name}: ${version}`);
        });
        console.log(`🛡️ Protection anti-cache: ACTIVÉE`);
        console.log(`===============================================`);
    },
    
    // DÉSACTIVÉ - Méthode pour vérifier si le cache doit être purgé (causait une boucle infinie)
    shouldPurgeCache() {
        // DÉSACTIVÉ pour éviter la boucle infinie de rechargement
        return false;
    },
    
    // DÉSACTIVÉ - Méthode pour forcer le rechargement si nécessaire
    forceReloadIfNeeded() {
        // DÉSACTIVÉ pour éviter la boucle infinie de rechargement
        console.log(`🛡️ Auto-reload DÉSACTIVÉ pour éviter les boucles infinies`);
        return false;
    },
    
    // Méthode pour vérifier l'intégrité des modules
    verifyModuleIntegrity() {
        const requiredModules = ['Player', 'World', 'Chunk', 'Controls'];
        const loadedModules = [];
        
        // Cette méthode sera appelée par chaque module lors de son chargement
        return {
            registerModule: (moduleName, version) => {
                loadedModules.push({ name: moduleName, version, timestamp: Date.now() });
                console.log(`✅ Module enregistré: ${moduleName} - ${version}`);
                
                // Vérifier si tous les modules requis sont chargés
                if (loadedModules.length >= requiredModules.length) {
                    console.log(`🎉 TOUS LES MODULES CHARGÉS AVEC SUCCÈS:`);
                    loadedModules.forEach(module => {
                        console.log(`   ✅ ${module.name}: ${module.version}`);
                    });
                }
            },
            
            getLoadedModules: () => loadedModules,
            
            isAllModulesLoaded: () => loadedModules.length >= requiredModules.length
        };
    },
    
    // Méthode pour détecter les conflits de cache
    detectCacheConflicts() {
        const userAgent = navigator.userAgent;
        const isChrome = userAgent.includes('Chrome');
        const isFirefox = userAgent.includes('Firefox');
        const isSafari = userAgent.includes('Safari') && !userAgent.includes('Chrome');
        
        console.log(`🌐 Navigateur détecté: ${isChrome ? 'Chrome' : isFirefox ? 'Firefox' : isSafari ? 'Safari' : 'Autre'}`);
        
        // Recommandations spécifiques par navigateur pour éviter le cache
        if (isChrome) {
            console.log(`💡 Chrome détecté - Pour forcer le rechargement: Ctrl+Shift+R ou F12 > Network > Disable cache`);
        } else if (isFirefox) {
            console.log(`💡 Firefox détecté - Pour forcer le rechargement: Ctrl+Shift+R ou F12 > Network > Disable cache`);
        } else if (isSafari) {
            console.log(`💡 Safari détecté - Pour forcer le rechargement: Cmd+Shift+R ou Develop > Disable caches`);
        }
        
        return { isChrome, isFirefox, isSafari };
    }
};

// Auto-affichage des informations de version
VERSION_CONFIG.displayVersionInfo();

// Vérification automatique du cache au chargement
if (VERSION_CONFIG.forceReloadIfNeeded()) {
    console.log(`⏳ Rechargement en cours pour purger le cache...`);
}