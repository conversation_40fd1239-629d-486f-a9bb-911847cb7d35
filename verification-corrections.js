// Script de vérification des corrections appliquées
console.log('🔍 Vérification des corrections de minage et logs...');

// Fonction pour vérifier les corrections dans les fichiers
async function verifyCorrections() {
    const results = {
        logger: false,
        player: false,
        controls: false,
        main: false
    };
    
    try {
        // Vérifier Logger.js
        const loggerResponse = await fetch('js/utils/Logger.js');
        const loggerContent = await loggerResponse.text();
        results.logger = loggerContent.includes('Seulement avant fermeture ou erreur critique') &&
                        loggerContent.includes('event.error.stack.includes(\'CRITICAL\')');
        
        // Vérifier Player.js
        const playerResponse = await fetch('js/player/Player.js');
        const playerContent = await playerResponse.text();
        results.player = playerContent.includes('startMining(world = null)') &&
                        playerContent.includes('const worldToUse = world || window.world') &&
                        playerContent.includes('logger.mining');
        
        // Vérifier Controls.js
        const controlsResponse = await fetch('js/player/Controls.js');
        const controlsContent = await controlsResponse.text();
        results.controls = controlsContent.includes('this.player.startMining(window.world)') &&
                          controlsContent.includes('e.preventDefault()') &&
                          controlsContent.includes('this.logger.mining');
        
        // Vérifier main.js
        const mainResponse = await fetch('js/main.js');
        const mainContent = await mainResponse.text();
        results.main = mainContent.includes('const isPointerLocked = document.pointerLockElement === document.body') &&
                      mainContent.includes('!isPointerLocked');
        
    } catch (error) {
        console.error('❌ Erreur lors de la vérification:', error);
        return results;
    }
    
    return results;
}

// Fonction pour afficher les résultats
function displayResults(results) {
    console.log('\n📊 Résultats de la vérification:');
    console.log('================================');
    
    const checks = [
        { name: 'Logger.js - Correction téléchargement automatique', passed: results.logger },
        { name: 'Player.js - Correction système de minage', passed: results.player },
        { name: 'Controls.js - Amélioration gestionnaires événements', passed: results.controls },
        { name: 'main.js - Prévention conflits de clics', passed: results.main }
    ];
    
    let passedCount = 0;
    checks.forEach(check => {
        const icon = check.passed ? '✅' : '❌';
        console.log(`${icon} ${check.name}`);
        if (check.passed) passedCount++;
    });
    
    console.log('================================');
    console.log(`📈 Score: ${passedCount}/${checks.length} corrections vérifiées`);
    
    if (passedCount === checks.length) {
        console.log('🎉 Toutes les corrections ont été appliquées avec succès !');
        console.log('\n🎮 Instructions pour tester:');
        console.log('1. Ouvrez http://localhost:8000/index.html');
        console.log('2. Cliquez pour verrouiller le pointeur');
        console.log('3. Vérifiez qu\'aucun log ne se télécharge automatiquement');
        console.log('4. Maintenez clic gauche sur un bloc pour miner');
        console.log('5. Vérifiez que la barre de progression apparaît');
        console.log('6. Vérifiez que le bloc disparaît après minage');
    } else {
        console.log('⚠️ Certaines corrections n\'ont pas été détectées.');
        console.log('Vérifiez les fichiers manuellement.');
    }
}

// Fonction pour tester le système de minage (si dans le contexte du jeu)
function testMiningSystem() {
    if (typeof window !== 'undefined' && window.GameLogger) {
        console.log('\n🧪 Test du système de minage...');
        
        // Vérifier que les méthodes existent
        const tests = [
            { name: 'GameLogger disponible', test: () => !!window.GameLogger },
            { name: 'Méthode mining disponible', test: () => typeof window.GameLogger.mining === 'function' },
            { name: 'Monde disponible', test: () => !!window.world },
            { name: 'Player disponible', test: () => !!window.player }
        ];
        
        tests.forEach(test => {
            const result = test.test();
            const icon = result ? '✅' : '❌';
            console.log(`${icon} ${test.name}`);
        });
        
        // Test de log de minage
        if (window.GameLogger && window.GameLogger.mining) {
            window.GameLogger.mining('Test du système de minage', {
                testMode: true,
                timestamp: Date.now()
            });
            console.log('✅ Test de log de minage effectué');
        }
    } else {
        console.log('ℹ️ Tests de minage non disponibles (contexte non-jeu)');
    }
}

// Fonction principale
async function runVerification() {
    console.log('🚀 Démarrage de la vérification...\n');
    
    const results = await verifyCorrections();
    displayResults(results);
    
    // Test supplémentaire si dans le contexte du jeu
    setTimeout(() => {
        testMiningSystem();
    }, 1000);
    
    return results;
}

// Exporter pour utilisation
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { runVerification, verifyCorrections, testMiningSystem };
}

// Auto-exécution si dans un navigateur
if (typeof window !== 'undefined') {
    window.runVerification = runVerification;
    window.verifyCorrections = verifyCorrections;
    window.testMiningSystem = testMiningSystem;
    
    // Exécuter automatiquement après un délai
    setTimeout(() => {
        runVerification();
    }, 500);
}

console.log('📋 Script de vérification chargé. Utilisez runVerification() pour lancer les tests.');
