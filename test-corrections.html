<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test des Corrections - J<PERSON>craft</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: #fff;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }
        .test-section.error {
            border-left-color: #f44336;
        }
        .test-section.warning {
            border-left-color: #ff9800;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .test-result.pass {
            background: #1b5e20;
            color: #c8e6c9;
        }
        .test-result.fail {
            background: #b71c1c;
            color: #ffcdd2;
        }
        .test-result.info {
            background: #0d47a1;
            color: #bbdefb;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #45a049;
        }
        #game-frame {
            width: 100%;
            height: 400px;
            border: 2px solid #4CAF50;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Test des Corrections JScraft</h1>
        
        <div class="test-section">
            <h2>📋 Tests Automatiques</h2>
            <button onclick="runAllTests()">Lancer tous les tests</button>
            <button onclick="testJumpLogic()">Test Logique de Saut</button>
            <button onclick="testFileCleanup()">Test Nettoyage Fichiers</button>
            <button onclick="runFinalVerification()">Vérification Finale Complète</button>
            <button onclick="testTerrainIssues()">Test Problèmes Terrain</button>
            <button onclick="generateTerrainReport()">Rapport Terrain</button>
            <button onclick="testEnvolsCorrection()" style="background: #ff5722;">🚀 Test Correction Envols</button>
            <div id="test-results"></div>
        </div>

        <div class="test-section">
            <h2>🎮 Test Interactif</h2>
            <p>Cliquez sur le bouton ci-dessous pour charger le jeu et tester manuellement :</p>
            <button onclick="loadGame()">Charger le Jeu</button>
            <iframe id="game-frame" src="about:blank" style="display:none;"></iframe>
        </div>

        <div class="test-section error">
            <h2>🚀 Tests Critiques Anti-Envols</h2>
            <p><strong>PROBLÈME RÉSOLU :</strong> Envols non désirés du joueur</p>
            <ol>
                <li><strong>Test Saut sur Place :</strong> Saut normal sans envol incontrôlé</li>
                <li><strong>Test Marche Plaines :</strong> Pas de trous, pas d'envols</li>
                <li><strong>Test Physique Stable :</strong> Mouvement prévisible</li>
                <li><strong>Test Monitoring :</strong> Détection automatique des problèmes</li>
            </ol>
            <button onclick="testEnvolsCorrection()" style="background: #ff5722; margin: 10px 0;">🚀 LANCER TEST ANTI-ENVOLS</button>
        </div>

        <div class="test-section">
            <h2>📊 Instructions de Test Manuel</h2>
            <ol>
                <li><strong>Test de Saut :</strong> Appuyez sur Espace et vérifiez qu'un seul saut est effectué</li>
                <li><strong>Test de Performance :</strong> Vérifiez que les FPS restent à 60</li>
                <li><strong>Test de Collision :</strong> Vérifiez que le joueur ne traverse pas le sol</li>
                <li><strong>Test de Cooldown :</strong> Maintenez Espace et vérifiez qu'il n'y a pas de spam de sauts</li>
            </ol>
        </div>
    </div>

    <script src="verification-finale.js"></script>
    <script src="diagnostic-terrain.js"></script>
    <script src="test-envols-correction.js"></script>
    <script>
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function testFileCleanup() {
            addTestResult('🧹 Test de nettoyage des fichiers...', 'info');
            
            const filesToCheck = [
                'CORRECTION_FINALE_COMPLETE.js',
                'CORRECTION_FINALE_PHYSIQUE.js', 
                'CORRECTION_SAUT_ET_COLLISION.js'
            ];
            
            let cleanupSuccess = true;
            
            filesToCheck.forEach(file => {
                fetch(file)
                    .then(response => {
                        if (response.ok) {
                            addTestResult(`❌ ÉCHEC: ${file} existe encore`, 'fail');
                            cleanupSuccess = false;
                        } else {
                            addTestResult(`✅ SUCCÈS: ${file} supprimé`, 'pass');
                        }
                    })
                    .catch(() => {
                        addTestResult(`✅ SUCCÈS: ${file} supprimé`, 'pass');
                    });
            });
            
            setTimeout(() => {
                if (cleanupSuccess) {
                    addTestResult('✅ Nettoyage des fichiers réussi', 'pass');
                }
            }, 1000);
        }

        function testJumpLogic() {
            addTestResult('🦘 Test de la logique de saut...', 'info');
            
            // Simuler le chargement du code de contrôles
            fetch('js/player/Controls.js')
                .then(response => response.text())
                .then(code => {
                    if (code.includes('jumpPressed') && code.includes('jumpCooldown')) {
                        addTestResult('✅ Variables de contrôle de saut présentes', 'pass');
                    } else {
                        addTestResult('❌ Variables de contrôle de saut manquantes', 'fail');
                    }
                    
                    if (code.includes('!this.jumpPressed') && code.includes('currentTime - this.lastJumpTime')) {
                        addTestResult('✅ Logique anti-spam de saut implémentée', 'pass');
                    } else {
                        addTestResult('❌ Logique anti-spam de saut manquante', 'fail');
                    }
                })
                .catch(error => {
                    addTestResult(`❌ Erreur lors du test: ${error.message}`, 'fail');
                });
        }

        function loadGame() {
            const iframe = document.getElementById('game-frame');
            iframe.src = 'index.html';
            iframe.style.display = 'block';
            addTestResult('🎮 Jeu chargé dans l\'iframe', 'info');
        }

        function testTerrainIssues() {
            addTestResult('🌍 Test des corrections terrain...', 'info');

            // Tester les corrections de grottes MISES À JOUR
            fetch('js/world/WorldGenerator.js')
                .then(response => response.text())
                .then(code => {
                    if (code.includes('y > 30 || y < 15') && code.includes('y > terrainHeight - 12')) {
                        addTestResult('✅ Corrections grottes appliquées (zone 15-30, protection 12 blocs)', 'pass');
                    } else {
                        addTestResult('❌ Corrections grottes manquantes', 'fail');
                    }

                    if (code.includes('caveNoise > 0.75')) {
                        addTestResult('✅ Seuil de grottes corrigé (0.75)', 'pass');
                    } else {
                        addTestResult('❌ Seuil de grottes incorrect', 'fail');
                    }

                    if (code.includes('biome.name === \'Plains\' && y > terrainHeight - 15')) {
                        addTestResult('✅ Protection spéciale plaines (15 blocs)', 'pass');
                    } else {
                        addTestResult('❌ Protection plaines manquante', 'fail');
                    }
                });

            // Tester les corrections de physique MISES À JOUR
            fetch('js/player/Player.js')
                .then(response => response.text())
                .then(code => {
                    if (code.includes('this.isJumping') && code.includes('this.jumpStartTime') && code.includes('this.jumpDuration')) {
                        addTestResult('✅ Système de saut robuste implémenté', 'pass');
                    } else {
                        addTestResult('❌ Système de saut robuste manquant', 'fail');
                    }

                    if (code.includes('distanceToGround <= 0.1')) {
                        addTestResult('✅ Seuil de détection sol maintenu (0.1)', 'pass');
                    } else {
                        addTestResult('❌ Seuil de détection sol incorrect', 'fail');
                    }

                    if (code.includes('SAUT EN COURS - Ne pas interférer')) {
                        addTestResult('✅ Protection pendant saut implémentée', 'pass');
                    } else {
                        addTestResult('❌ Protection pendant saut manquante', 'fail');
                    }
                });
        }

        function runAllTests() {
            addTestResult('🚀 Démarrage de tous les tests...', 'info');
            testFileCleanup();
            setTimeout(() => testJumpLogic(), 500);
            setTimeout(() => testTerrainIssues(), 1000);
            addTestResult('📝 Tests terminés. Vérifiez les résultats ci-dessus.', 'info');
        }

        // Lancer les tests automatiquement au chargement
        window.addEventListener('load', () => {
            addTestResult('🔧 Page de test chargée', 'info');
            addTestResult('💡 Cliquez sur "Lancer tous les tests" pour commencer', 'info');
        });
    </script>
</body>
</html>
