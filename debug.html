<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Debug Minecraft JS</title>
    <style>
        body { margin: 0; background: #000; color: white; font-family: monospace; }
        #debug { position: absolute; top: 10px; left: 10px; z-index: 100; white-space: pre-line; }
        canvas { display: block; }
    </style>
</head>
<body>
    <div id="debug">Chargement...</div>
    <canvas id="game"></canvas>

    <script src="https://unpkg.com/three@0.155.0/build/three.min.js"></script>
    <script src="js/utils/SimplexNoise.js"></script>
    <script type="module">
        const debug = document.getElementById('debug');
        
        function log(message) {
            debug.textContent += message + '\n';
            console.log(message);
        }
        
        try {
            log('1. Vérification Three.js...');
            if (!window.THREE) throw new Error('Three.js non chargé');
            log('✓ Three.js version ' + THREE.REVISION);
            
            log('2. Vérification SimplexNoise...');
            if (!window.SimplexNoise) throw new Error('SimplexNoise non chargé');
            const noise = new SimplexNoise(42);
            const testNoise = noise.noise2D(0, 0);
            log('✓ SimplexNoise test: ' + testNoise.toFixed(3));
            
            log('3. Création de la scène...');
            const scene = new THREE.Scene();
            scene.background = new THREE.Color(0x87CEEB);
            
            const canvas = document.getElementById('game');
            const renderer = new THREE.WebGLRenderer({ canvas: canvas });
            renderer.setSize(window.innerWidth, window.innerHeight);
            log('✓ Renderer créé');
            
            log('4. Import des modules...');
            
            // Test d'import des modules un par un
            import('./js/world/WorldGenerator.js').then(({ WorldGenerator }) => {
                log('✓ WorldGenerator importé');
                const generator = new WorldGenerator();
                log('✓ WorldGenerator instancié');
                
                return import('./js/world/Chunk.js');
            }).then(({ Chunk }) => {
                log('✓ Chunk importé');
                
                return import('./js/utils/WorkerManager.js');
            }).then(({ WorkerManager }) => {
                log('✓ WorkerManager importé');
                
                return import('./js/world/World.js');
            }).then(({ World }) => {
                log('✓ World importé');
                log('5. Création du monde...');
                
                const world = new World(scene);
                log('✓ World créé');
                
                return import('./js/player/Player.js');
            }).then(({ Player }) => {
                log('✓ Player importé');
                log('6. Création du joueur...');
                
                const player = new Player(scene);
                log('✓ Player créé');
                
                return import('./js/player/Controls.js');
            }).then(({ Controls }) => {
                log('✓ Controls importé');
                log('7. Création des contrôles...');
                
                const controls = new Controls(player, document.body);
                log('✓ Controls créés');
                
                log('8. Ajout de l\'éclairage...');
                const light = new THREE.DirectionalLight(0xffffff, 1);
                light.position.set(1, 1, 1);
                scene.add(light);
                scene.add(new THREE.AmbientLight(0x404040));
                log('✓ Éclairage ajouté');
                
                log('9. Démarrage de l\'animation...');
                let frameCount = 0;
                
                function animate() {
                    requestAnimationFrame(animate);
                    
                    if (frameCount % 60 === 0) {
                        log(`Frame ${frameCount}, objets: ${scene.children.length}`);
                    }
                    
                    renderer.render(scene, player.camera);
                    frameCount++;
                }
                
                animate();
                log('✓ Jeu démarré avec succès !');
                
            }).catch(error => {
                log('✗ Erreur: ' + error.message);
                log('Stack: ' + error.stack);
            });
            
        } catch (error) {
            log('✗ Erreur critique: ' + error.message);
            log('Stack: ' + error.stack);
        }
    </script>
</body>
</html>