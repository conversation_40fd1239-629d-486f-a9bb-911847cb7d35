<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Test Immédiat - Corrections Minage</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
        }
        .error { border-left-color: #f44336; }
        .warning { border-left-color: #ff9800; }
        .info { border-left-color: #2196F3; }
        pre {
            background: #333;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            max-height: 300px;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #45a049; }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            margin: 5px 0;
        }
        .success { background: #4CAF50; }
        .fail { background: #f44336; }
        #console-output {
            background: #000;
            color: #0f0;
            font-family: 'Courier New', monospace;
            padding: 10px;
            border-radius: 3px;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🔧 Test Immédiat des Corrections</h1>
    
    <div class="test-section">
        <h2>🎯 Tests Rapides</h2>
        <button onclick="testGameLoaded()">1. Vérifier Chargement</button>
        <button onclick="testChunks()">2. Tester Chunks</button>
        <button onclick="testMining()">3. Tester Minage</button>
        <button onclick="testPhysics()">4. Tester Physique</button>
        <button onclick="runAllTests()">🚀 Tout Tester</button>
        <div id="test-results"></div>
    </div>

    <div class="test-section">
        <h2>🖥️ Console en Temps Réel</h2>
        <button onclick="clearConsole()">Effacer</button>
        <button onclick="toggleConsole()">Activer/Désactiver</button>
        <div id="console-output"></div>
    </div>

    <div class="test-section">
        <h2>🎮 Actions Directes</h2>
        <button onclick="forceChunkGeneration()">Forcer Génération Chunks</button>
        <button onclick="simulateMining()">Simuler Minage</button>
        <button onclick="showGameState()">État du Jeu</button>
        <button onclick="openMainGame()">Ouvrir Jeu Principal</button>
    </div>

    <script>
        let consoleActive = true;
        let originalConsoleLog = console.log;
        let originalConsoleError = console.error;
        let originalConsoleWarn = console.warn;

        // Intercepter les logs de la console
        function setupConsoleInterception() {
            const consoleOutput = document.getElementById('console-output');
            
            console.log = function(...args) {
                if (consoleActive) {
                    const message = args.join(' ');
                    const timestamp = new Date().toLocaleTimeString();
                    consoleOutput.innerHTML += `<div>[${timestamp}] ${message}</div>`;
                    consoleOutput.scrollTop = consoleOutput.scrollHeight;
                }
                originalConsoleLog.apply(console, args);
            };
            
            console.error = function(...args) {
                if (consoleActive) {
                    const message = args.join(' ');
                    const timestamp = new Date().toLocaleTimeString();
                    consoleOutput.innerHTML += `<div style="color: #ff6b6b;">[${timestamp}] ERROR: ${message}</div>`;
                    consoleOutput.scrollTop = consoleOutput.scrollHeight;
                }
                originalConsoleError.apply(console, args);
            };
            
            console.warn = function(...args) {
                if (consoleActive) {
                    const message = args.join(' ');
                    const timestamp = new Date().toLocaleTimeString();
                    consoleOutput.innerHTML += `<div style="color: #ffa500;">[${timestamp}] WARN: ${message}</div>`;
                    consoleOutput.scrollTop = consoleOutput.scrollHeight;
                }
                originalConsoleWarn.apply(console, args);
            };
        }

        function clearConsole() {
            document.getElementById('console-output').innerHTML = '';
        }

        function toggleConsole() {
            consoleActive = !consoleActive;
            console.log(`Console interception ${consoleActive ? 'activée' : 'désactivée'}`);
        }

        function displayResult(testName, success, details = '') {
            const results = document.getElementById('test-results');
            const icon = success ? '✅' : '❌';
            const className = success ? 'success' : 'fail';
            results.innerHTML += `<div class="status ${className}">${icon} ${testName} ${details}</div>`;
        }

        function testGameLoaded() {
            console.log('🧪 Test: Chargement du jeu...');
            
            // Ouvrir le jeu principal dans un iframe caché pour tester
            const iframe = document.createElement('iframe');
            iframe.src = 'index.html';
            iframe.style.display = 'none';
            document.body.appendChild(iframe);
            
            setTimeout(() => {
                try {
                    const gameWindow = iframe.contentWindow;
                    const hasThree = !!gameWindow.THREE;
                    const hasWorld = !!gameWindow.world;
                    const hasPlayer = !!gameWindow.player;
                    
                    displayResult('Chargement du jeu', hasThree && hasWorld && hasPlayer, 
                        `THREE: ${hasThree}, World: ${hasWorld}, Player: ${hasPlayer}`);
                    
                    document.body.removeChild(iframe);
                } catch (error) {
                    displayResult('Chargement du jeu', false, `Erreur: ${error.message}`);
                    document.body.removeChild(iframe);
                }
            }, 5000);
        }

        function testChunks() {
            console.log('🧪 Test: Chunks...');
            
            // Test via l'API fetch pour vérifier les scripts
            fetch('js/world/World.js')
                .then(response => response.text())
                .then(content => {
                    const hasGenerateVisibleChunks = content.includes('generateVisibleChunks()');
                    const hasForceGeneration = content.includes('Forçage de génération');
                    
                    displayResult('Corrections Chunks', hasGenerateVisibleChunks && hasForceGeneration,
                        `generateVisibleChunks: ${hasGenerateVisibleChunks}, Force: ${hasForceGeneration}`);
                })
                .catch(error => {
                    displayResult('Corrections Chunks', false, `Erreur: ${error.message}`);
                });
        }

        function testMining() {
            console.log('🧪 Test: Minage...');
            
            Promise.all([
                fetch('js/player/Player.js').then(r => r.text()),
                fetch('js/player/Controls.js').then(r => r.text())
            ])
            .then(([playerContent, controlsContent]) => {
                const hasMiningSensitivity = controlsContent.includes('0.0008');
                const hasMiningLogs = playerContent.includes('logger.mining');
                const hasWorldParam = playerContent.includes('startMining(world = null)');
                
                displayResult('Corrections Minage', hasMiningSensitivity && hasMiningLogs && hasWorldParam,
                    `Sensibilité: ${hasMiningSensitivity}, Logs: ${hasMiningLogs}, Param: ${hasWorldParam}`);
            })
            .catch(error => {
                displayResult('Corrections Minage', false, `Erreur: ${error.message}`);
            });
        }

        function testPhysics() {
            console.log('🧪 Test: Physique...');
            
            fetch('js/player/Player.js')
                .then(response => response.text())
                .then(content => {
                    const hasReducedGravity = content.includes('12 * delta') && content.includes('8 * delta');
                    const hasReducedJump = content.includes('velocity.y = 8');
                    
                    displayResult('Corrections Physique', hasReducedGravity && hasReducedJump,
                        `Gravité réduite: ${hasReducedGravity}, Saut réduit: ${hasReducedJump}`);
                })
                .catch(error => {
                    displayResult('Corrections Physique', false, `Erreur: ${error.message}`);
                });
        }

        function runAllTests() {
            document.getElementById('test-results').innerHTML = '<h3>🚀 Exécution de tous les tests...</h3>';
            
            testGameLoaded();
            setTimeout(() => testChunks(), 1000);
            setTimeout(() => testMining(), 2000);
            setTimeout(() => testPhysics(), 3000);
            
            setTimeout(() => {
                console.log('✅ Tous les tests terminés');
            }, 6000);
        }

        function forceChunkGeneration() {
            console.log('🌍 Tentative de forçage de génération de chunks...');
            
            // Essayer d'accéder au jeu principal
            try {
                if (window.opener && window.opener.world) {
                    const result = window.opener.world.generateVisibleChunks();
                    console.log(`✅ ${result} chunks générés`);
                } else {
                    console.log('⚠️ Jeu principal non accessible depuis cette fenêtre');
                }
            } catch (error) {
                console.error('❌ Erreur:', error.message);
            }
        }

        function simulateMining() {
            console.log('⛏️ Simulation de minage...');
            
            try {
                if (window.opener && window.opener.player) {
                    window.opener.player.startMining(window.opener.world);
                    setTimeout(() => {
                        window.opener.player.stopMining();
                        console.log('✅ Simulation de minage terminée');
                    }, 2000);
                } else {
                    console.log('⚠️ Player non accessible depuis cette fenêtre');
                }
            } catch (error) {
                console.error('❌ Erreur:', error.message);
            }
        }

        function showGameState() {
            console.log('📊 État du jeu:');
            
            try {
                if (window.opener) {
                    const state = {
                        hasThree: !!window.opener.THREE,
                        hasWorld: !!window.opener.world,
                        hasPlayer: !!window.opener.player,
                        hasControls: !!window.opener.controls,
                        chunks: window.opener.world ? window.opener.world.chunks.size : 0,
                        pointerLocked: !!window.opener.document.pointerLockElement
                    };
                    
                    console.log('État:', JSON.stringify(state, null, 2));
                } else {
                    console.log('⚠️ Jeu principal non accessible');
                }
            } catch (error) {
                console.error('❌ Erreur:', error.message);
            }
        }

        function openMainGame() {
            window.open('index.html', '_blank');
            console.log('🎮 Jeu principal ouvert dans un nouvel onglet');
        }

        // Initialisation
        setupConsoleInterception();
        console.log('🔧 Page de test chargée - Prêt pour les tests');
        
        // Auto-test après 2 secondes
        setTimeout(() => {
            console.log('🚀 Démarrage des tests automatiques...');
            runAllTests();
        }, 2000);
    </script>
</body>
</html>
