// WorkerManager.js - Gestion des Web Workers pour la génération de chunks
export class WorkerManager {
    constructor() {
        this.workers = [];
        this.maxWorkers = navigator.hardwareConcurrency || 4;
        this.taskQueue = [];
        this.activeTasks = new Map(); // taskId -> { worker, resolve, reject }
        this.initWorkers();
    }

    initWorkers() {
        for (let i = 0; i < this.maxWorkers; i++) {
            const worker = new Worker('js/workers/ChunkWorker.js');
            worker.onmessage = (e) => this.handleWorkerMessage(e);
            this.workers.push(worker);
        }
    }

    generateChunk(chunkX, chunkZ) {
        return new Promise((resolve, reject) => {
            const taskId = `${chunkX},${chunkZ}`;

            // Vérifier si cette tâche est déjà en cours
            if (this.activeTasks.has(taskId)) {
                // Ajouter les callbacks à la tâche existante
                const existingTask = this.activeTasks.get(taskId);
                if (!existingTask.additionalCallbacks) {
                    existingTask.additionalCallbacks = [];
                }
                existingTask.additionalCallbacks.push({ resolve, reject });
                return;
            }

            // Vérifier si cette tâche est déjà dans la file d'attente
            const existingInQueue = this.taskQueue.find(task => task.id === taskId);
            if (existingInQueue) {
                // Ajouter les callbacks à la tâche en file d'attente
                if (!existingInQueue.additionalCallbacks) {
                    existingInQueue.additionalCallbacks = [];
                }
                existingInQueue.additionalCallbacks.push({ resolve, reject });
                return;
            }

            // Ajouter à la file d'attente
            this.taskQueue.push({
                id: taskId,
                chunkX,
                chunkZ,
                resolve,
                reject
            });

            this.processQueue();
        });
    }

    processQueue() {
        // Trouver un worker disponible
        const availableWorker = this.workers.find(worker =>
            !Array.from(this.activeTasks.values()).some(task => task.worker === worker)
        );

        if (availableWorker && this.taskQueue.length > 0) {
            const task = this.taskQueue.shift();

            // Stocker la tâche active avec le worker et les callbacks
            this.activeTasks.set(task.id, {
                worker: availableWorker,
                resolve: task.resolve,
                reject: task.reject,
                additionalCallbacks: task.additionalCallbacks || []
            });

            availableWorker.postMessage({
                type: 'generate',
                chunkX: task.chunkX,
                chunkZ: task.chunkZ
            });
        }
    }

    handleWorkerMessage(e) {
        const { type, chunkX, chunkZ, blocks } = e.data;

        if (type === 'chunkGenerated') {
            const taskId = `${chunkX},${chunkZ}`;

            // Vérifier si cette tâche est dans les tâches actives
            if (this.activeTasks.has(taskId)) {
                const task = this.activeTasks.get(taskId);

                // Résoudre la promesse principale avec les blocs
                task.resolve(blocks);

                // Résoudre toutes les promesses additionnelles
                if (task.additionalCallbacks) {
                    task.additionalCallbacks.forEach(callback => {
                        callback.resolve(blocks);
                    });
                }

                // Supprimer la tâche active
                this.activeTasks.delete(taskId);

                // Traiter la file d'attente pour la prochaine tâche
                this.processQueue();
            } else {
                console.warn(`Task ${taskId} completed but not found in active tasks`);
            }
        }
    }

    dispose() {
        this.workers.forEach(worker => worker.terminate());
        this.workers = [];
        this.activeTasks.clear();
        this.taskQueue = [];
    }
} 