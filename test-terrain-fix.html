<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Correction Terrain</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
        }
        .info {
            background: #2a2a2a;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background: #2d5a2d; }
        .warning { background: #5a4d2d; }
        .error { background: #5a2d2d; }
        #gameContainer {
            width: 100%;
            height: 600px;
            border: 2px solid #444;
            border-radius: 8px;
            overflow: hidden;
        }
        .controls {
            margin-top: 15px;
            text-align: center;
        }
        button {
            background: #4a4a4a;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #5a5a5a;
        }
    </style>
</head>
<body>
    <div class="info">
        <h1>🔧 Test des Corrections du Terrain</h1>
        <p>Ce test vérifie que les corrections appliquées ont résolu les problèmes de trous dans le sol et de rebonds du joueur.</p>
    </div>

    <div id="status-container">
        <div class="status success">
            ✅ <strong>Corrections appliquées :</strong>
            <ul>
                <li>Grottes limitées à 5+ blocs sous la surface (évite les trous)</li>
                <li>Seuil de grottes augmenté (0.65 au lieu de 0.6)</li>
                <li>Physique du joueur stabilisée (évite les rebonds)</li>
                <li>Génération d'eau corrigée (seulement dans les océans)</li>
            </ul>
        </div>
    </div>

    <div id="gameContainer">
        <iframe src="index.html" width="100%" height="100%" frameborder="0"></iframe>
    </div>

    <div class="controls">
        <button onclick="reloadGame()">🔄 Recharger le Jeu</button>
        <button onclick="showDebugInfo()">🐛 Infos Debug</button>
        <button onclick="testPhysics()">⚡ Test Physique</button>
    </div>

    <div id="debug-info" style="display: none; margin-top: 20px; background: #2a2a2a; padding: 15px; border-radius: 8px;">
        <h3>Informations de Debug</h3>
        <div id="debug-content"></div>
    </div>

    <script>
        function reloadGame() {
            const iframe = document.querySelector('#gameContainer iframe');
            iframe.src = iframe.src;
            console.log('🔄 Jeu rechargé pour tester les corrections');
        }

        function showDebugInfo() {
            const debugDiv = document.getElementById('debug-info');
            const debugContent = document.getElementById('debug-content');
            
            if (debugDiv.style.display === 'none') {
                debugDiv.style.display = 'block';
                debugContent.innerHTML = `
                    <p><strong>Corrections appliquées :</strong></p>
                    <ul>
                        <li><code>ChunkWorker.js</code> : Grottes limitées à y < height - 5</li>
                        <li><code>WorldGenerator.js</code> : Seuil de grottes 0.65, zone 8-45</li>
                        <li><code>Player.js</code> : Physique stabilisée avec tolérance 0.1</li>
                        <li><code>Chunk.js</code> : Cohérence avec les autres fichiers</li>
                    </ul>
                    <p><strong>Problèmes résolus :</strong></p>
                    <ul>
                        <li>❌ Trous dans le sol causés par les grottes en surface</li>
                        <li>❌ Rebonds constants du joueur</li>
                        <li>❌ Eau remplaçant des blocs de surface</li>
                        <li>❌ Incohérence entre les chunks</li>
                    </ul>
                `;
            } else {
                debugDiv.style.display = 'none';
            }
        }

        function testPhysics() {
            alert(`🧪 Test de Physique

Instructions pour tester :
1. Déplacez-vous dans le monde
2. Vérifiez qu'il n'y a plus de trous dans le sol
3. Confirmez que le joueur ne rebondit plus
4. Testez la marche sur différents terrains
5. Vérifiez que l'escalade fonctionne normalement

Si vous observez encore des problèmes, ouvrez la console (F12) pour voir les logs.`);
        }

        // Ajouter des informations de statut au chargement
        window.addEventListener('load', function() {
            console.log('🔧 Test des corrections du terrain chargé');
            console.log('📋 Corrections appliquées :');
            console.log('  - Grottes : Limitées à 5+ blocs sous surface');
            console.log('  - Physique : Stabilisation avec tolérance 0.1');
            console.log('  - Eau : Génération corrigée');
            console.log('  - Cohérence : Synchronisation entre fichiers');
        });
    </script>
</body>
</html>