// TEST CORRECTION ENVOLS - Validation des corrections critiques
console.log('🚀 TEST CORRECTION ENVOLS - Démarrage...');

class EnvolsTestSuite {
    constructor() {
        this.testResults = {
            jumpLogicFixed: false,
            physicsStabilized: false,
            caveGenerationSecured: false,
            movementSeparated: false,
            noDoubleMovement: false
        };
        
        this.playerMonitoring = {
            unexpectedFlights: 0,
            jumpCount: 0,
            positionHistory: [],
            velocitySpikes: 0
        };
        
        this.startTesting();
    }
    
    async startTesting() {
        console.log('🔍 Démarrage des tests de correction d\'envols...');
        
        // Tests de code
        await this.testJumpLogic();
        await this.testPhysicsStability();
        await this.testCaveGeneration();
        await this.testMovementSeparation();
        
        // Monitoring en temps réel
        this.startPlayerMonitoring();
        
        this.generateTestReport();
    }
    
    async testJumpLogic() {
        console.log('🦘 Test de la logique de saut...');
        
        try {
            const response = await fetch('js/player/Controls.js');
            const code = await response.text();
            
            const hasReducedJumpForce = code.includes('this.player.velocity.y = 12');
            const hasJumpFlag = code.includes('this.player.isJumping = true');
            const hasJumpStartTime = code.includes('this.player.jumpStartTime = currentTime');
            const hasCooldown = code.includes('this.jumpCooldown');
            
            this.testResults.jumpLogicFixed = hasReducedJumpForce && hasJumpFlag && hasJumpStartTime && hasCooldown;
            
            console.log(`  - Force de saut réduite (12): ${hasReducedJumpForce ? '✅' : '❌'}`);
            console.log(`  - Flag de saut: ${hasJumpFlag ? '✅' : '❌'}`);
            console.log(`  - Timestamp de saut: ${hasJumpStartTime ? '✅' : '❌'}`);
            console.log(`  - Cooldown: ${hasCooldown ? '✅' : '❌'}`);
            
            console.log(`🦘 Logique de saut: ${this.testResults.jumpLogicFixed ? 'SUCCÈS' : 'ÉCHEC'}`);
            
        } catch (error) {
            console.error('❌ Erreur test logique saut:', error);
            this.testResults.jumpLogicFixed = false;
        }
    }
    
    async testPhysicsStability() {
        console.log('⚡ Test de la stabilité physique...');
        
        try {
            const response = await fetch('js/player/Player.js');
            const code = await response.text();
            
            const hasJumpDuration = code.includes('this.jumpDuration = 500');
            const hasJumpProtection = code.includes('if (this.isJumping && (currentTime - this.jumpStartTime) < this.jumpDuration)');
            const hasMovementSeparation = code.includes('// 1. Appliquer le mouvement horizontal');
            const hasVerticalFirst = code.includes('// 2. Appliquer le mouvement vertical AVANT la physique');
            const hasNoDoubleMovement = !code.includes('this.camera.position.y += movement.y;') || 
                                       code.split('this.camera.position.y += movement.y;').length <= 2;
            
            this.testResults.physicsStabilized = hasJumpDuration && hasJumpProtection;
            this.testResults.movementSeparated = hasMovementSeparation && hasVerticalFirst;
            this.testResults.noDoubleMovement = hasNoDoubleMovement;
            
            console.log(`  - Durée de saut définie: ${hasJumpDuration ? '✅' : '❌'}`);
            console.log(`  - Protection pendant saut: ${hasJumpProtection ? '✅' : '❌'}`);
            console.log(`  - Mouvement séparé: ${hasMovementSeparation ? '✅' : '❌'}`);
            console.log(`  - Pas de double mouvement Y: ${hasNoDoubleMovement ? '✅' : '❌'}`);
            
            console.log(`⚡ Stabilité physique: ${this.testResults.physicsStabilized ? 'SUCCÈS' : 'ÉCHEC'}`);
            
        } catch (error) {
            console.error('❌ Erreur test physique:', error);
            this.testResults.physicsStabilized = false;
        }
    }
    
    async testCaveGeneration() {
        console.log('🕳️ Test de la génération de grottes...');
        
        try {
            const [worldGenResponse, chunkWorkerResponse] = await Promise.all([
                fetch('js/world/WorldGenerator.js'),
                fetch('js/workers/ChunkWorker.js')
            ]);
            
            const worldGenCode = await worldGenResponse.text();
            const chunkWorkerCode = await chunkWorkerResponse.text();
            
            const hasRestrictedZone = worldGenCode.includes('y > 30 || y < 15');
            const hasDeepProtection = worldGenCode.includes('y > terrainHeight - 12');
            const hasPlainsProtection = worldGenCode.includes('biome.name === \'Plains\' && y > terrainHeight - 15');
            const hasHighThreshold = worldGenCode.includes('caveNoise > 0.75');
            const hasChunkProtection = chunkWorkerCode.includes('y < height - 12');
            
            this.testResults.caveGenerationSecured = hasRestrictedZone && hasDeepProtection && 
                                                   hasPlainsProtection && hasHighThreshold && hasChunkProtection;
            
            console.log(`  - Zone restreinte (15-30): ${hasRestrictedZone ? '✅' : '❌'}`);
            console.log(`  - Protection profonde (12 blocs): ${hasDeepProtection ? '✅' : '❌'}`);
            console.log(`  - Protection plaines (15 blocs): ${hasPlainsProtection ? '✅' : '❌'}`);
            console.log(`  - Seuil élevé (0.75): ${hasHighThreshold ? '✅' : '❌'}`);
            console.log(`  - Protection chunk: ${hasChunkProtection ? '✅' : '❌'}`);
            
            console.log(`🕳️ Génération grottes: ${this.testResults.caveGenerationSecured ? 'SUCCÈS' : 'ÉCHEC'}`);
            
        } catch (error) {
            console.error('❌ Erreur test grottes:', error);
            this.testResults.caveGenerationSecured = false;
        }
    }
    
    async testMovementSeparation() {
        console.log('🔄 Test de la séparation des mouvements...');
        
        try {
            const response = await fetch('js/player/Player.js');
            const code = await response.text();
            
            const hasHorizontalFirst = code.includes('// 1. Appliquer le mouvement horizontal (X et Z) d\'abord');
            const hasVerticalSecond = code.includes('// 2. Appliquer le mouvement vertical AVANT la physique');
            const hasPhysicsThird = code.includes('// 3. Système de physique robuste APRÈS le mouvement');
            const hasCollisionFourth = code.includes('// 4. Collision horizontale simplifiée');
            
            this.testResults.movementSeparated = hasHorizontalFirst && hasVerticalSecond && 
                                               hasPhysicsThird && hasCollisionFourth;
            
            console.log(`  - Mouvement horizontal d'abord: ${hasHorizontalFirst ? '✅' : '❌'}`);
            console.log(`  - Mouvement vertical ensuite: ${hasVerticalSecond ? '✅' : '❌'}`);
            console.log(`  - Physique après: ${hasPhysicsThird ? '✅' : '❌'}`);
            console.log(`  - Collision en dernier: ${hasCollisionFourth ? '✅' : '❌'}`);
            
            console.log(`🔄 Séparation mouvement: ${this.testResults.movementSeparated ? 'SUCCÈS' : 'ÉCHEC'}`);
            
        } catch (error) {
            console.error('❌ Erreur test mouvement:', error);
            this.testResults.movementSeparated = false;
        }
    }
    
    startPlayerMonitoring() {
        console.log('👁️ Démarrage du monitoring joueur...');
        
        const monitorPlayer = () => {
            if (!window.player || !window.player.camera) {
                requestAnimationFrame(monitorPlayer);
                return;
            }
            
            const currentPos = {
                x: window.player.camera.position.x,
                y: window.player.camera.position.y,
                z: window.player.camera.position.z,
                velocity: {
                    x: window.player.velocity.x,
                    y: window.player.velocity.y,
                    z: window.player.velocity.z
                },
                onGround: window.player.onGround,
                isJumping: window.player.isJumping,
                timestamp: Date.now()
            };
            
            // Ajouter à l'historique
            this.playerMonitoring.positionHistory.push(currentPos);
            if (this.playerMonitoring.positionHistory.length > 120) { // 2 secondes à 60 FPS
                this.playerMonitoring.positionHistory.shift();
            }
            
            // Détecter les envols non désirés
            this.detectUnexpectedFlights(currentPos);
            
            // Détecter les pics de vélocité
            this.detectVelocitySpikes(currentPos);
            
            requestAnimationFrame(monitorPlayer);
        };
        
        requestAnimationFrame(monitorPlayer);
    }
    
    detectUnexpectedFlights(currentPos) {
        if (this.playerMonitoring.positionHistory.length < 10) return;
        
        const recent = this.playerMonitoring.positionHistory.slice(-10);
        
        // Détecter un envol : montée rapide sans saut intentionnel
        let rapidAscent = 0;
        for (let i = 1; i < recent.length; i++) {
            const prev = recent[i - 1];
            const curr = recent[i];
            
            if (curr.y > prev.y + 0.5 && !curr.isJumping && curr.velocity.y > 5) {
                rapidAscent++;
            }
        }
        
        if (rapidAscent > 3) {
            this.playerMonitoring.unexpectedFlights++;
            console.warn('🚨 ENVOL NON DÉSIRÉ DÉTECTÉ:', {
                position: currentPos,
                rapidAscentFrames: rapidAscent,
                isJumping: currentPos.isJumping,
                velocity: currentPos.velocity
            });
        }
    }
    
    detectVelocitySpikes(currentPos) {
        if (Math.abs(currentPos.velocity.y) > 20 && !currentPos.isJumping) {
            this.playerMonitoring.velocitySpikes++;
            console.warn('⚡ PIC DE VÉLOCITÉ DÉTECTÉ:', {
                velocity: currentPos.velocity,
                position: currentPos,
                isJumping: currentPos.isJumping
            });
        }
    }
    
    generateTestReport() {
        console.log('\n📋 RAPPORT TEST CORRECTION ENVOLS');
        console.log('=====================================');
        
        const totalTests = Object.keys(this.testResults).length;
        const passedTests = Object.values(this.testResults).filter(result => result).length;
        const successRate = Math.round((passedTests / totalTests) * 100);
        
        console.log(`✅ Tests réussis: ${passedTests}/${totalTests} (${successRate}%)`);
        console.log('\nDétail des résultats:');
        
        Object.entries(this.testResults).forEach(([test, result]) => {
            const status = result ? '✅ SUCCÈS' : '❌ ÉCHEC';
            const testName = test.replace(/([A-Z])/g, ' $1').toLowerCase();
            console.log(`  ${status}: ${testName}`);
        });
        
        console.log('\n📊 Monitoring en temps réel:');
        console.log(`  - Envols non désirés: ${this.playerMonitoring.unexpectedFlights}`);
        console.log(`  - Pics de vélocité: ${this.playerMonitoring.velocitySpikes}`);
        
        if (successRate === 100 && this.playerMonitoring.unexpectedFlights === 0) {
            console.log('\n🎉 CORRECTION DES ENVOLS RÉUSSIE!');
            console.log('Le joueur ne devrait plus s\'envoler de manière non désirée.');
        } else {
            console.log('\n⚠️ CORRECTIONS PARTIELLES APPLIQUÉES');
            console.log('Certains problèmes peuvent persister.');
        }
        
        // Rendre le rapport disponible globalement
        window.envolsTestReport = {
            testResults: this.testResults,
            monitoring: this.playerMonitoring,
            successRate: successRate,
            timestamp: new Date().toISOString()
        };
        
        console.log('\n💡 Rapport disponible dans: window.envolsTestReport');
    }
}

// Démarrer les tests automatiquement
window.addEventListener('load', () => {
    setTimeout(() => {
        new EnvolsTestSuite();
    }, 3000);
});

// Export pour utilisation manuelle
window.testEnvolsCorrection = () => new EnvolsTestSuite();
