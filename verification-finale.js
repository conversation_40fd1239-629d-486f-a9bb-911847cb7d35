// VÉRIFICATION FINALE - Script de validation des corrections
console.log('🔍 VÉRIFICATION FINALE - Démarrage...');

class FinalVerification {
    constructor() {
        this.results = {
            fileCleanup: false,
            jumpLogicFixed: false,
            groundDetectionFixed: false,
            performanceOptimized: false,
            noConflicts: false
        };
        
        this.runAllVerifications();
    }
    
    async runAllVerifications() {
        console.log('🚀 Démarrage de toutes les vérifications...');
        
        await this.verifyFileCleanup();
        await this.verifyJumpLogic();
        await this.verifyGroundDetection();
        await this.verifyPerformance();
        await this.verifyNoConflicts();
        
        this.generateFinalReport();
    }
    
    async verifyFileCleanup() {
        console.log('🧹 Vérification du nettoyage des fichiers...');
        
        const filesToCheck = [
            'CORRECTION_FINALE_COMPLETE.js',
            'CORRECTION_FINALE_PHYSIQUE.js',
            'CORRECTION_SAUT_ET_COLLISION.js'
        ];
        
        let allFilesRemoved = true;
        
        for (const file of filesToCheck) {
            try {
                const response = await fetch(file);
                if (response.ok) {
                    console.error(`❌ ${file} existe encore`);
                    allFilesRemoved = false;
                } else {
                    console.log(`✅ ${file} correctement supprimé`);
                }
            } catch (error) {
                console.log(`✅ ${file} correctement supprimé`);
            }
        }
        
        this.results.fileCleanup = allFilesRemoved;
        console.log(`🧹 Nettoyage des fichiers: ${allFilesRemoved ? 'SUCCÈS' : 'ÉCHEC'}`);
    }
    
    async verifyJumpLogic() {
        console.log('🦘 Vérification de la logique de saut...');
        
        try {
            const response = await fetch('js/player/Controls.js');
            const code = await response.text();
            
            const hasJumpPressed = code.includes('this.jumpPressed');
            const hasJumpCooldown = code.includes('this.jumpCooldown');
            const hasAntiSpamLogic = code.includes('!this.jumpPressed') && code.includes('currentTime - this.lastJumpTime');
            const hasJumpReset = code.includes('this.jumpPressed = false');
            
            const jumpLogicFixed = hasJumpPressed && hasJumpCooldown && hasAntiSpamLogic && hasJumpReset;
            
            console.log(`  - Variables de contrôle: ${hasJumpPressed && hasJumpCooldown ? '✅' : '❌'}`);
            console.log(`  - Logique anti-spam: ${hasAntiSpamLogic ? '✅' : '❌'}`);
            console.log(`  - Réinitialisation: ${hasJumpReset ? '✅' : '❌'}`);
            
            this.results.jumpLogicFixed = jumpLogicFixed;
            console.log(`🦘 Logique de saut: ${jumpLogicFixed ? 'SUCCÈS' : 'ÉCHEC'}`);
            
        } catch (error) {
            console.error('❌ Erreur lors de la vérification de la logique de saut:', error);
            this.results.jumpLogicFixed = false;
        }
    }
    
    async verifyGroundDetection() {
        console.log('🌍 Vérification de la détection du sol...');
        
        try {
            const response = await fetch('js/player/Player.js');
            const code = await response.text();
            
            const hasPreciseThreshold = code.includes('distanceToGround <= 0.1');
            const hasVelocityCheck = code.includes('this.velocity.y <= 0');
            const hasConditionalGravity = code.includes('if (this.velocity.y <= 0)');
            
            const groundDetectionFixed = hasPreciseThreshold && hasVelocityCheck && hasConditionalGravity;
            
            console.log(`  - Seuil précis (0.1): ${hasPreciseThreshold ? '✅' : '❌'}`);
            console.log(`  - Vérification vélocité: ${hasVelocityCheck ? '✅' : '❌'}`);
            console.log(`  - Gravité conditionnelle: ${hasConditionalGravity ? '✅' : '❌'}`);
            
            this.results.groundDetectionFixed = groundDetectionFixed;
            console.log(`🌍 Détection du sol: ${groundDetectionFixed ? 'SUCCÈS' : 'ÉCHEC'}`);
            
        } catch (error) {
            console.error('❌ Erreur lors de la vérification de la détection du sol:', error);
            this.results.groundDetectionFixed = false;
        }
    }
    
    async verifyPerformance() {
        console.log('📊 Vérification des performances...');
        
        // Simuler une vérification de performance basique
        const startTime = performance.now();
        let frameCount = 0;
        
        return new Promise((resolve) => {
            const checkFrame = () => {
                frameCount++;
                if (frameCount < 60) {
                    requestAnimationFrame(checkFrame);
                } else {
                    const endTime = performance.now();
                    const duration = endTime - startTime;
                    const fps = Math.round(60000 / duration);
                    
                    const performanceOptimized = fps >= 50;
                    
                    console.log(`  - FPS estimé: ${fps}`);
                    console.log(`  - Durée pour 60 frames: ${Math.round(duration)}ms`);
                    
                    this.results.performanceOptimized = performanceOptimized;
                    console.log(`📊 Performance: ${performanceOptimized ? 'SUCCÈS' : 'ÉCHEC'}`);
                    
                    resolve();
                }
            };
            
            requestAnimationFrame(checkFrame);
        });
    }
    
    async verifyNoConflicts() {
        console.log('⚔️ Vérification de l\'absence de conflits...');
        
        try {
            const htmlResponse = await fetch('index.html');
            const htmlCode = await htmlResponse.text();
            
            const noAutoCorrection = !htmlCode.includes('CORRECTION_FINALE_COMPLETE.js');
            const noAutoFix = !htmlCode.includes('localStorage.setItem(\'autoFixPhysics\'');
            
            const noConflicts = noAutoCorrection && noAutoFix;
            
            console.log(`  - Pas de chargement auto de correction: ${noAutoCorrection ? '✅' : '❌'}`);
            console.log(`  - Pas d'auto-fix activé: ${noAutoFix ? '✅' : '❌'}`);
            
            this.results.noConflicts = noConflicts;
            console.log(`⚔️ Absence de conflits: ${noConflicts ? 'SUCCÈS' : 'ÉCHEC'}`);
            
        } catch (error) {
            console.error('❌ Erreur lors de la vérification des conflits:', error);
            this.results.noConflicts = false;
        }
    }
    
    generateFinalReport() {
        console.log('\n📋 RAPPORT FINAL DE VÉRIFICATION');
        console.log('=====================================');
        
        const totalTests = Object.keys(this.results).length;
        const passedTests = Object.values(this.results).filter(result => result).length;
        const successRate = Math.round((passedTests / totalTests) * 100);
        
        console.log(`✅ Tests réussis: ${passedTests}/${totalTests} (${successRate}%)`);
        console.log('\nDétail des résultats:');
        
        Object.entries(this.results).forEach(([test, result]) => {
            const status = result ? '✅ SUCCÈS' : '❌ ÉCHEC';
            const testName = test.replace(/([A-Z])/g, ' $1').toLowerCase();
            console.log(`  ${status}: ${testName}`);
        });
        
        if (successRate === 100) {
            console.log('\n🎉 TOUTES LES CORRECTIONS SONT APPLIQUÉES AVEC SUCCÈS!');
            console.log('Le jeu devrait maintenant fonctionner normalement.');
        } else {
            console.log('\n⚠️ CERTAINES CORRECTIONS NÉCESSITENT UNE ATTENTION');
            console.log('Vérifiez les échecs ci-dessus et corrigez si nécessaire.');
        }
        
        // Rendre le rapport disponible globalement
        window.finalVerificationReport = {
            results: this.results,
            successRate: successRate,
            timestamp: new Date().toISOString()
        };
        
        console.log('\n💡 Rapport disponible dans: window.finalVerificationReport');
    }
}

// Démarrer la vérification automatiquement
window.addEventListener('load', () => {
    setTimeout(() => {
        new FinalVerification();
    }, 1000);
});

// Export pour utilisation manuelle
window.runFinalVerification = () => new FinalVerification();
