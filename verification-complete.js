// VÉRIFICATION COMPLÈTE - Validation de toutes les corrections
console.log('🔍 VÉRIFICATION COMPLÈTE - Démarrage...');

class CompleteVerification {
    constructor() {
        this.results = {
            // Corrections précédentes
            fileCleanup: false,
            jumpLogicFixed: false,
            groundDetectionFixed: false,
            performanceOptimized: false,
            noConflicts: false,
            
            // Nouvelles corrections terrain
            caveGenerationFixed: false,
            waterGenerationFixed: false,
            physicsStabilized: false,
            collisionImproved: false
        };
        
        this.runCompleteVerification();
    }
    
    async runCompleteVerification() {
        console.log('🚀 Démarrage de la vérification complète...');
        
        // Vérifications précédentes
        await this.verifyFileCleanup();
        await this.verifyJumpLogic();
        await this.verifyGroundDetection();
        await this.verifyPerformance();
        await this.verifyNoConflicts();
        
        // Nouvelles vérifications terrain
        await this.verifyCaveGeneration();
        await this.verifyWaterGeneration();
        await this.verifyPhysicsStability();
        await this.verifyCollisionImprovement();
        
        this.generateCompleteReport();
    }
    
    async verifyFileCleanup() {
        console.log('🧹 Vérification du nettoyage des fichiers...');
        
        const filesToCheck = [
            'CORRECTION_FINALE_COMPLETE.js',
            'CORRECTION_FINALE_PHYSIQUE.js',
            'CORRECTION_SAUT_ET_COLLISION.js'
        ];
        
        let allFilesRemoved = true;
        
        for (const file of filesToCheck) {
            try {
                const response = await fetch(file);
                if (response.ok) {
                    console.error(`❌ ${file} existe encore`);
                    allFilesRemoved = false;
                }
            } catch (error) {
                // Fichier n'existe pas - c'est ce qu'on veut
            }
        }
        
        this.results.fileCleanup = allFilesRemoved;
        console.log(`🧹 Nettoyage des fichiers: ${allFilesRemoved ? 'SUCCÈS' : 'ÉCHEC'}`);
    }
    
    async verifyJumpLogic() {
        console.log('🦘 Vérification de la logique de saut...');
        
        try {
            const response = await fetch('js/player/Controls.js');
            const code = await response.text();
            
            const hasJumpPressed = code.includes('this.jumpPressed');
            const hasJumpCooldown = code.includes('this.jumpCooldown');
            const hasAntiSpamLogic = code.includes('!this.jumpPressed') && code.includes('currentTime - this.lastJumpTime');
            
            this.results.jumpLogicFixed = hasJumpPressed && hasJumpCooldown && hasAntiSpamLogic;
            console.log(`🦘 Logique de saut: ${this.results.jumpLogicFixed ? 'SUCCÈS' : 'ÉCHEC'}`);
            
        } catch (error) {
            console.error('❌ Erreur lors de la vérification de la logique de saut:', error);
            this.results.jumpLogicFixed = false;
        }
    }
    
    async verifyGroundDetection() {
        console.log('🌍 Vérification de la détection du sol...');
        
        try {
            const response = await fetch('js/player/Player.js');
            const code = await response.text();
            
            const hasPrediction = code.includes('predictedY') && code.includes('distanceToPredictedGround');
            const hasImprovedThreshold = code.includes('distanceToGround <= 0.2');
            const hasStabilization = code.includes('stabilisation immédiate');
            
            this.results.groundDetectionFixed = hasPrediction && hasImprovedThreshold;
            console.log(`🌍 Détection du sol: ${this.results.groundDetectionFixed ? 'SUCCÈS' : 'ÉCHEC'}`);
            
        } catch (error) {
            console.error('❌ Erreur lors de la vérification de la détection du sol:', error);
            this.results.groundDetectionFixed = false;
        }
    }
    
    async verifyPerformance() {
        console.log('📊 Vérification des performances...');
        
        const startTime = performance.now();
        let frameCount = 0;
        
        return new Promise((resolve) => {
            const checkFrame = () => {
                frameCount++;
                if (frameCount < 60) {
                    requestAnimationFrame(checkFrame);
                } else {
                    const endTime = performance.now();
                    const duration = endTime - startTime;
                    const fps = Math.round(60000 / duration);
                    
                    this.results.performanceOptimized = fps >= 50;
                    console.log(`📊 Performance: ${this.results.performanceOptimized ? 'SUCCÈS' : 'ÉCHEC'} (${fps} FPS)`);
                    resolve();
                }
            };
            requestAnimationFrame(checkFrame);
        });
    }
    
    async verifyNoConflicts() {
        console.log('⚔️ Vérification de l\'absence de conflits...');
        
        try {
            const htmlResponse = await fetch('index.html');
            const htmlCode = await htmlResponse.text();
            
            const noAutoCorrection = !htmlCode.includes('CORRECTION_FINALE_COMPLETE.js');
            const cleanComment = htmlCode.includes('Scripts de correction supprimés');
            
            this.results.noConflicts = noAutoCorrection && cleanComment;
            console.log(`⚔️ Absence de conflits: ${this.results.noConflicts ? 'SUCCÈS' : 'ÉCHEC'}`);
            
        } catch (error) {
            console.error('❌ Erreur lors de la vérification des conflits:', error);
            this.results.noConflicts = false;
        }
    }
    
    async verifyCaveGeneration() {
        console.log('🕳️ Vérification de la génération de grottes...');
        
        try {
            const [worldGenResponse, chunkWorkerResponse] = await Promise.all([
                fetch('js/world/WorldGenerator.js'),
                fetch('js/workers/ChunkWorker.js')
            ]);
            
            const worldGenCode = await worldGenResponse.text();
            const chunkWorkerCode = await chunkWorkerResponse.text();
            
            const hasReducedZone = worldGenCode.includes('y > 35 || y < 10');
            const hasTerrainCheck = worldGenCode.includes('y > terrainHeight - 8');
            const hasHigherThreshold = worldGenCode.includes('caveNoise > 0.7');
            const hasProtection = chunkWorkerCode.includes('y < height - 8');
            
            this.results.caveGenerationFixed = hasReducedZone && hasTerrainCheck && hasHigherThreshold && hasProtection;
            console.log(`🕳️ Génération de grottes: ${this.results.caveGenerationFixed ? 'SUCCÈS' : 'ÉCHEC'}`);
            
        } catch (error) {
            console.error('❌ Erreur lors de la vérification des grottes:', error);
            this.results.caveGenerationFixed = false;
        }
    }
    
    async verifyWaterGeneration() {
        console.log('💧 Vérification de la génération d\'eau...');
        
        try {
            const [chunkResponse, chunkWorkerResponse] = await Promise.all([
                fetch('js/world/Chunk.js'),
                fetch('js/workers/ChunkWorker.js')
            ]);
            
            const chunkCode = await chunkResponse.text();
            const chunkWorkerCode = await chunkWorkerResponse.text();
            
            const hasDepthRestriction = chunkCode.includes('height < waterLevel - 5');
            const hasOceanOnly = chunkCode.includes('biome.name === \'Ocean\' && height < waterLevel - 5');
            const workerHasRestriction = chunkWorkerCode.includes('height < waterLevel - 5');
            
            this.results.waterGenerationFixed = hasDepthRestriction && hasOceanOnly && workerHasRestriction;
            console.log(`💧 Génération d\'eau: ${this.results.waterGenerationFixed ? 'SUCCÈS' : 'ÉCHEC'}`);
            
        } catch (error) {
            console.error('❌ Erreur lors de la vérification de l\'eau:', error);
            this.results.waterGenerationFixed = false;
        }
    }
    
    async verifyPhysicsStability() {
        console.log('⚡ Vérification de la stabilité physique...');
        
        try {
            const response = await fetch('js/player/Player.js');
            const code = await response.text();
            
            const hasPredictivePhysics = code.includes('predictedY') && code.includes('predictedFeetY');
            const hasAnticipatedLanding = code.includes('Atterrissage anticipé');
            const hasReducedGravity = code.includes('25 * delta'); // Gravité réduite
            const hasSpeedLimit = code.includes('this.velocity.y < -25'); // Vitesse limitée
            
            this.results.physicsStabilized = hasPredictivePhysics && hasAnticipatedLanding && hasReducedGravity;
            console.log(`⚡ Stabilité physique: ${this.results.physicsStabilized ? 'SUCCÈS' : 'ÉCHEC'}`);
            
        } catch (error) {
            console.error('❌ Erreur lors de la vérification de la physique:', error);
            this.results.physicsStabilized = false;
        }
    }
    
    async verifyCollisionImprovement() {
        console.log('🎯 Vérification des améliorations de collision...');
        
        try {
            const response = await fetch('js/world/World.js');
            const code = await response.text();
            
            const hasBoxCollision = code.includes('Math.abs(x - (blockWorldX + 0.5))');
            const hasStableDetection = code.includes('Collision en boîte plutôt qu\'en sphère');
            const hasSimplifiedCheck = code.includes('offsetY = 0; offsetY <= 1');
            
            this.results.collisionImproved = hasBoxCollision && hasStableDetection;
            console.log(`🎯 Amélioration collision: ${this.results.collisionImproved ? 'SUCCÈS' : 'ÉCHEC'}`);
            
        } catch (error) {
            console.error('❌ Erreur lors de la vérification des collisions:', error);
            this.results.collisionImproved = false;
        }
    }
    
    generateCompleteReport() {
        console.log('\n📋 RAPPORT COMPLET DE VÉRIFICATION');
        console.log('=====================================');
        
        const totalTests = Object.keys(this.results).length;
        const passedTests = Object.values(this.results).filter(result => result).length;
        const successRate = Math.round((passedTests / totalTests) * 100);
        
        console.log(`✅ Tests réussis: ${passedTests}/${totalTests} (${successRate}%)`);
        console.log('\nDétail des résultats:');
        
        // Grouper les résultats
        const categories = {
            'Corrections Précédentes': ['fileCleanup', 'jumpLogicFixed', 'groundDetectionFixed', 'performanceOptimized', 'noConflicts'],
            'Corrections Terrain': ['caveGenerationFixed', 'waterGenerationFixed', 'physicsStabilized', 'collisionImproved']
        };
        
        Object.entries(categories).forEach(([category, tests]) => {
            console.log(`\n${category}:`);
            tests.forEach(test => {
                const result = this.results[test];
                const status = result ? '✅ SUCCÈS' : '❌ ÉCHEC';
                const testName = test.replace(/([A-Z])/g, ' $1').toLowerCase();
                console.log(`  ${status}: ${testName}`);
            });
        });
        
        if (successRate === 100) {
            console.log('\n🎉 TOUTES LES CORRECTIONS SONT APPLIQUÉES AVEC SUCCÈS!');
            console.log('Le jeu devrait maintenant fonctionner parfaitement.');
        } else if (successRate >= 80) {
            console.log('\n✅ LA MAJORITÉ DES CORRECTIONS SONT APPLIQUÉES');
            console.log('Le jeu devrait fonctionner correctement avec des améliorations mineures possibles.');
        } else {
            console.log('\n⚠️ CERTAINES CORRECTIONS NÉCESSITENT UNE ATTENTION');
            console.log('Vérifiez les échecs ci-dessus et corrigez si nécessaire.');
        }
        
        // Rendre le rapport disponible globalement
        window.completeVerificationReport = {
            results: this.results,
            successRate: successRate,
            timestamp: new Date().toISOString(),
            categories: categories
        };
        
        console.log('\n💡 Rapport complet disponible dans: window.completeVerificationReport');
    }
}

// Démarrer la vérification automatiquement
window.addEventListener('load', () => {
    setTimeout(() => {
        new CompleteVerification();
    }, 2000);
});

// Export pour utilisation manuelle
window.runCompleteVerification = () => new CompleteVerification();
