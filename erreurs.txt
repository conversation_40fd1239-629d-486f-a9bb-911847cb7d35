[CHUNK] Donn<PERSON> reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4837 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 4837 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4839 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 4839 }
Logger.js:102:21
[DEBUG] Mouvement bloquÃ© par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[DEBUG] Mouvement bloquÃ© par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[DEBUG] Mouvement bloquÃ© par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 43, timestamp: 4875 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 42, timestamp: 4875 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4888 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 4888 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4889 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 4889 }
Logger.js:102:21
[DEBUG] Mouvement bloquÃ© par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[DEBUG] Mouvement bloquÃ© par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[DEBUG] Mouvement bloquÃ© par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 41, timestamp: 4926 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 40, timestamp: 4926 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4938 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 4938 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4939 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 4939 }
Logger.js:102:21
[DEBUG] Mouvement bloquÃ© par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[DEBUG] Mouvement bloquÃ© par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[DEBUG] Mouvement bloquÃ© par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 39, timestamp: 4976 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 38, timestamp: 4976 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4989 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 4989 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4990 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 4990 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 8, wasOnGround: true, flyMode: false, cooldownRemaining: 200, jumpStartTime: 1753276306210, timestamp: 4992 }
Logger.js:102:21
[DEBUG] Mouvement bloquÃ© par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[DEBUG] Mouvement bloquÃ© par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[DEBUG] Mouvement bloquÃ© par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 37, timestamp: 5026 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 36, timestamp: 5026 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5037 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5037 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5038 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5039 }
Logger.js:102:21
[DEBUG] Mouvement bloquÃ© par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[DEBUG] Mouvement bloquÃ© par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 35, timestamp: 5076 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 34, timestamp: 5076 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5087 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5087 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5088 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5088 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 33, timestamp: 5125 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 32, timestamp: 5125 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5137 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5137 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5138 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5138 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 31, timestamp: 5175 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 30, timestamp: 5175 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5187 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5187 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5188 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5188 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 29, timestamp: 5226 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 28, timestamp: 5226 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5237 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5237 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5237 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5238 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 27, timestamp: 5275 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 26, timestamp: 5276 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5286 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5286 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5287 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5288 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 5325 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 99, timestamp: 5365 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 98, timestamp: 5365 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5378 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5378 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5379 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5379 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 5409 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 99, timestamp: 5409 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 98, timestamp: 5409 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5423 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5423 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5424 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5424 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 97, timestamp: 5459 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 96, timestamp: 5459 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5471 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5471 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5472 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5473 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 95, timestamp: 5509 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 94, timestamp: 5509 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5534 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5534 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5535 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5535 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 93, timestamp: 5559 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 92, timestamp: 5559 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5570 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5570 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5573 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5573 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 91, timestamp: 5610 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 90, timestamp: 5610 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5622 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5622 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5623 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5623 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 5659 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 99, timestamp: 5732 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 98, timestamp: 5732 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5753 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5753 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5754 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5755 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 97, timestamp: 5777 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 96, timestamp: 5778 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5800 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5801 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5802 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5802 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 95, timestamp: 5826 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 94, timestamp: 5826 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5837 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5838 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5839 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5839 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 93, timestamp: 5876 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 92, timestamp: 5876 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5887 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5887 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5888 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5888 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 91, timestamp: 5925 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 90, timestamp: 5925 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5937 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5938 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5939 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5939 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 89, timestamp: 5976 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 88, timestamp: 5976 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5989 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5989 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 5990 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 5990 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 87, timestamp: 6026 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 86, timestamp: 6026 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6038 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6038 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6040 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6040 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 85, timestamp: 6076 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 84, timestamp: 6076 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6087 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6087 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6088 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6088 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 83, timestamp: 6126 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 82, timestamp: 6126 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6136 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6137 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6138 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6138 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 81, timestamp: 6176 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 80, timestamp: 6176 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6186 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6187 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6188 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6188 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 79, timestamp: 6225 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 78, timestamp: 6225 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6237 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6237 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6238 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6238 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 77, timestamp: 6275 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 76, timestamp: 6275 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6287 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6287 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6288 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6288 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 75, timestamp: 6325 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 74, timestamp: 6325 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6336 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6337 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6338 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6338 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 73, timestamp: 6376 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 72, timestamp: 6376 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6387 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6387 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6388 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6388 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 71, timestamp: 6425 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 70, timestamp: 6425 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6438 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6438 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6439 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6440 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 69, timestamp: 6475 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 68, timestamp: 6475 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6487 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6487 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6488 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6488 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 67, timestamp: 6525 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 66, timestamp: 6525 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6537 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6537 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6539 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6539 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 65, timestamp: 6575 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 64, timestamp: 6575 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6589 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6589 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6590 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6590 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 63, timestamp: 6626 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 62, timestamp: 6626 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6637 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6637 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6638 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6638 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 61, timestamp: 6676 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 60, timestamp: 6676 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6687 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6687 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6688 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6688 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 59, timestamp: 6726 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 58, timestamp: 6726 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6737 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6737 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6738 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6738 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 57, timestamp: 6775 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 56, timestamp: 6776 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6787 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6787 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6789 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6789 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 55, timestamp: 6826 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 54, timestamp: 6826 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 161, cameraPosition: {…}, fps: 60, frameCount: 360 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6837 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6838 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6838 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6838 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 53, timestamp: 6876 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 52, timestamp: 6876 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6887 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6887 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6888 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6888 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 51, timestamp: 6926 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 50, timestamp: 6926 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6937 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6937 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6938 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6938 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 49, timestamp: 6975 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 48, timestamp: 6976 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6987 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6987 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6988 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 6988 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 47, timestamp: 7026 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 46, timestamp: 7026 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7036 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7037 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7037 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7037 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 45, timestamp: 7076 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 44, timestamp: 7076 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7086 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7087 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7087 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7087 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 43, timestamp: 7125 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 42, timestamp: 7126 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7137 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7137 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7138 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7138 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 41, timestamp: 7176 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 40, timestamp: 7176 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7188 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7188 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7189 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7189 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 39, timestamp: 7226 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 38, timestamp: 7226 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7237 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7238 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7238 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7238 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 37, timestamp: 7276 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 36, timestamp: 7276 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7288 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7288 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7289 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7289 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 35, timestamp: 7326 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 34, timestamp: 7326 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7341 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7341 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7342 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7343 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 33, timestamp: 7376 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 32, timestamp: 7376 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7389 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7389 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7390 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7390 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 31, timestamp: 7425 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 30, timestamp: 7425 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7438 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7438 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7442 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7442 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 7475 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 99, timestamp: 7568 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 98, timestamp: 7568 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7595 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7595 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7596 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7596 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 97, timestamp: 7625 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 96, timestamp: 7625 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7636 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7636 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7637 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7637 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 95, timestamp: 7676 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 94, timestamp: 7676 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7687 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7687 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7688 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7688 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 93, timestamp: 7725 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 92, timestamp: 7726 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7737 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7737 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7739 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7739 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 91, timestamp: 7775 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 90, timestamp: 7776 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7787 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7787 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7789 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7789 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 89, timestamp: 7825 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 88, timestamp: 7825 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7837 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7837 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7838 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7838 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 87, timestamp: 7876 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 86, timestamp: 7876 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7887 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7887 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7888 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7888 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 85, timestamp: 7926 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 84, timestamp: 7926 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7936 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7937 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7937 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7937 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 83, timestamp: 7975 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 82, timestamp: 7975 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7987 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7987 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7988 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7988 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 81, timestamp: 8026 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 80, timestamp: 8026 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8038 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8038 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8039 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8039 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 79, timestamp: 8075 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 78, timestamp: 8075 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8088 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8088 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8089 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8089 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 77, timestamp: 8126 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 76, timestamp: 8126 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8141 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8141 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8142 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8143 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 75, timestamp: 8176 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 74, timestamp: 8176 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8188 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8189 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8189 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8190 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 73, timestamp: 8225 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 72, timestamp: 8225 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8248 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8249 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8250 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8250 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 71, timestamp: 8276 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 70, timestamp: 8276 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8287 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8287 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8290 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8291 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 69, timestamp: 8326 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 68, timestamp: 8326 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8338 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8338 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8339 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8339 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 67, timestamp: 8376 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 66, timestamp: 8376 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8387 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8388 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8389 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8389 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 65, timestamp: 8426 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 64, timestamp: 8426 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8437 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8437 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8438 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8438 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 63, timestamp: 8476 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 62, timestamp: 8476 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8486 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8488 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8489 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8489 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 61, timestamp: 8526 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 60, timestamp: 8526 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8538 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8539 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8539 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8540 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 59, timestamp: 8576 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 58, timestamp: 8576 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8588 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8588 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8589 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8589 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 57, timestamp: 8626 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 56, timestamp: 8626 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8638 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8638 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8639 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8639 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 55, timestamp: 8676 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 54, timestamp: 8676 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8688 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8688 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8689 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8689 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 53, timestamp: 8726 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 52, timestamp: 8726 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8738 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8739 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8740 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8740 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 51, timestamp: 8775 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 50, timestamp: 8776 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8788 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8788 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8789 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8789 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 49, timestamp: 8826 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 48, timestamp: 8826 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8838 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8838 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8839 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8839 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 47, timestamp: 8875 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 46, timestamp: 8876 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8888 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8889 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8892 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8892 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 45, timestamp: 8926 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 44, timestamp: 8926 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 173, cameraPosition: {…}, fps: 60, frameCount: 480 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8937 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8937 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8938 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8938 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 8975 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 99, timestamp: 9023 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 98, timestamp: 9023 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9038 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9038 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9039 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9039 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 97, timestamp: 9062 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 96, timestamp: 9062 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9073 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9073 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9074 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9074 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 95, timestamp: 9109 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 94, timestamp: 9109 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9121 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9121 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9122 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9123 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 93, timestamp: 9159 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 92, timestamp: 9159 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9172 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9172 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9173 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9174 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 91, timestamp: 9209 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 90, timestamp: 9209 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9220 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9220 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9222 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9222 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 9259 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 99, timestamp: 9259 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 98, timestamp: 9259 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9272 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9272 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9273 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9273 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 97, timestamp: 9309 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 96, timestamp: 9309 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9320 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9321 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9322 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9323 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 95, timestamp: 9360 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 94, timestamp: 9360 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9371 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9371 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9372 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9372 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 93, timestamp: 9409 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 92, timestamp: 9409 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9422 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9422 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9423 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9423 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 91, timestamp: 9459 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 90, timestamp: 9460 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9472 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9472 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9473 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9473 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 89, timestamp: 9509 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 88, timestamp: 9509 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9521 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9521 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9522 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9523 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 87, timestamp: 9559 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 86, timestamp: 9559 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9570 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9570 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9572 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9572 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 85, timestamp: 9609 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 84, timestamp: 9609 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9622 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9623 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9623 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9624 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 83, timestamp: 9659 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 82, timestamp: 9659 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9670 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9670 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9671 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9671 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 81, timestamp: 9709 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 80, timestamp: 9709 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9723 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9723 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9724 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9724 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 79, timestamp: 9758 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 78, timestamp: 9758 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9771 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9771 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9772 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9772 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 77, timestamp: 9809 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 76, timestamp: 9810 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9821 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9821 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9824 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9824 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 75, timestamp: 9858 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 74, timestamp: 9858 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9873 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9873 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9874 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9874 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 73, timestamp: 9909 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 72, timestamp: 9909 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9921 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9921 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9922 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9922 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 71, timestamp: 9959 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 70, timestamp: 9959 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9971 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9971 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9974 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9974 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 69, timestamp: 10010 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 68, timestamp: 10010 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10021 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10021 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10022 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10022 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 10059 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 99, timestamp: 10129 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 98, timestamp: 10129 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10149 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10149 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10150 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10150 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 97, timestamp: 10176 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 96, timestamp: 10176 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10187 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10187 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10189 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10190 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 95, timestamp: 10226 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 94, timestamp: 10226 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10248 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10248 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10249 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10249 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 93, timestamp: 10275 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 92, timestamp: 10276 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10287 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10288 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10289 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10289 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 91, timestamp: 10326 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 90, timestamp: 10326 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10337 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10337 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10338 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10338 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 89, timestamp: 10375 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 88, timestamp: 10375 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10386 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10386 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10387 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10388 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 87, timestamp: 10425 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 86, timestamp: 10425 }
Logger.js:102:21
[DEBUG] Mouvement bloquÃ© par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10449 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10449 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10450 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10450 }
Logger.js:102:21
[DEBUG] Mouvement bloquÃ© par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 85, timestamp: 10475 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 84, timestamp: 10475 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10488 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10488 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10489 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10490 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 83, timestamp: 10526 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 82, timestamp: 10526 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10538 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10538 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10539 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10541 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 81, timestamp: 10576 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 80, timestamp: 10576 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10587 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10587 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10588 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10588 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 79, timestamp: 10625 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 78, timestamp: 10626 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10637 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10637 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10638 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10638 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 77, timestamp: 10676 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 76, timestamp: 10676 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10688 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10688 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10689 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10689 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 10726 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 89, timestamp: 10765 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 88, timestamp: 10766 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10780 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10780 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10781 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10781 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 87, timestamp: 10809 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 86, timestamp: 10809 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10821 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10821 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10822 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10822 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 85, timestamp: 10859 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 84, timestamp: 10859 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10871 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10871 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10872 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10872 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 83, timestamp: 10909 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 82, timestamp: 10910 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10921 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10921 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10923 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10923 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 81, timestamp: 10959 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 80, timestamp: 10959 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10970 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10970 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10971 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10971 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 79, timestamp: 11010 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 78, timestamp: 11010 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11021 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11022 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11023 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11023 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 77, timestamp: 11059 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 76, timestamp: 11060 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 193, cameraPosition: {…}, fps: 60, frameCount: 600 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11070 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11070 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11071 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11071 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 11109 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 77, timestamp: 11109 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 76, timestamp: 11109 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11121 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11122 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11123 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11123 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 75, timestamp: 11159 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 74, timestamp: 11159 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11172 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11172 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11173 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11174 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 73, timestamp: 11209 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 72, timestamp: 11209 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11221 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11222 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11223 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11223 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 71, timestamp: 11259 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 70, timestamp: 11259 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11271 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11271 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11272 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11272 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 69, timestamp: 11309 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 68, timestamp: 11309 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11321 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11321 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11322 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11322 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 11359 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 67, timestamp: 11359 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 66, timestamp: 11360 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11372 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11372 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11373 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11374 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 65, timestamp: 11409 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 64, timestamp: 11410 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11422 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11423 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11424 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11424 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 63, timestamp: 11459 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 62, timestamp: 11459 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11475 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11475 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11476 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11476 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 61, timestamp: 11509 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 60, timestamp: 11509 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11519 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11520 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11521 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11521 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 59, timestamp: 11559 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 58, timestamp: 11559 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11571 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11571 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11572 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11572 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 57, timestamp: 11609 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 56, timestamp: 11609 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11622 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11622 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11623 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11623 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 55, timestamp: 11659 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 54, timestamp: 11659 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11671 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11672 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11673 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11673 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 53, timestamp: 11709 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 52, timestamp: 11709 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11723 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11723 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11723 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11724 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 51, timestamp: 11759 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 50, timestamp: 11759 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11772 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11772 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11773 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11773 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 49, timestamp: 11809 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 48, timestamp: 11809 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11821 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11821 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11822 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11823 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 47, timestamp: 11859 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 46, timestamp: 11859 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11871 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11871 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11872 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11873 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 45, timestamp: 11909 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 44, timestamp: 11909 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11921 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11921 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11922 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11922 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 43, timestamp: 11958 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 42, timestamp: 11959 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11970 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11971 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 11972 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 11972 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 41, timestamp: 12009 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 40, timestamp: 12009 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12020 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12020 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12023 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12023 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 39, timestamp: 12059 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 38, timestamp: 12059 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12070 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12070 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12072 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12072 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 37, timestamp: 12109 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 36, timestamp: 12109 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12120 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12121 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12122 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12122 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 35, timestamp: 12159 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 34, timestamp: 12159 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12171 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12172 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12173 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12173 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 33, timestamp: 12209 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 32, timestamp: 12209 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12221 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12221 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12222 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12223 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 31, timestamp: 12259 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 30, timestamp: 12259 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12272 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12272 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12275 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12275 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 29, timestamp: 12309 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 28, timestamp: 12309 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12323 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12323 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12325 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12325 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 27, timestamp: 12359 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 26, timestamp: 12359 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12371 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12371 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12372 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12372 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 25, timestamp: 12409 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 24, timestamp: 12410 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12422 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12422 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12423 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12423 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 23, timestamp: 12459 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 22, timestamp: 12460 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12473 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12473 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12474 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12474 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 21, timestamp: 12508 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 20, timestamp: 12509 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12521 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12521 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12522 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12523 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 19, timestamp: 12558 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 18, timestamp: 12558 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12570 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12570 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12571 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12571 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 17, timestamp: 12609 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 16, timestamp: 12609 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12622 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12622 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12624 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12624 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 15, timestamp: 12659 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 14, timestamp: 12659 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12670 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12671 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12672 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12672 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 13, timestamp: 12708 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 12, timestamp: 12709 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12731 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12732 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12733 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12733 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 11, timestamp: 12759 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 10, timestamp: 12759 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12771 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12771 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12773 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12773 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 9, timestamp: 12809 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 8, timestamp: 12809 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12820 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12821 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12824 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12824 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 7, timestamp: 12859 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 6, timestamp: 12859 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12870 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12870 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12871 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12871 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 5, timestamp: 12909 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 4, timestamp: 12909 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12922 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12922 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12923 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12923 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 3, timestamp: 12959 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 2, timestamp: 12960 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12971 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12971 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 12972 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 12973 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 1, timestamp: 13010 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 0, timestamp: 13010 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13023 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13023 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13024 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13024 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 193, cameraPosition: {…}, fps: 60, frameCount: 720 }
Logger.js:102:21
[MINING] 🖱️ Mousedown détecté 
Object { button: 0, pointerLocked: true, windowWorldExists: true, playerExists: true, timestamp: 13522 }
Logger.js:102:21
[MINING] ✅ Début du minage (clic gauche) 
Object { button: 0, pointerLocked: true, worldChunks: 481, timestamp: 13522 }
Logger.js:102:21
[MINING] 🔨 startMining appelé 
Object { isMining: false, inventoryOpen: false, worldProvided: true, windowWorldExists: true, timestamp: 13522 }
Logger.js:102:21
[MINING] 🎯 Recherche de bloc cible... 
Object { timestamp: 13522 }
Logger.js:102:21
[MINING] 🎯 getTargetBlock appelé 
Object { worldChunks: 481, cameraPosition: {…}, timestamp: 13522 }
Logger.js:102:21
[DEBUG] Nombre de meshes Ã  tester 
Object { count: 1017 }
Logger.js:102:21
[MINING] 📊 Meshes trouvés pour raycast 
Object { count: 1017, timestamp: 13523 }
Logger.js:102:21
[MINING] 🎯 Direction du raycast 
Object { origin: {…}, direction: {…}, timestamp: 13523 }
Logger.js:102:21
Uncaught TypeError: can't access property "set", this.raycaster is undefined
    getTargetBlock http://localhost:8000/js/player/Player.js?v=1753276301489:913
    startMining http://localhost:8000/js/player/Player.js?v=1753276301489:831
    Controls http://localhost:8000/js/player/Controls.js?v=1753276301489:113
Player.js:913:9
[MINING] Arrêt du minage (relâchement clic) 
Object { button: 0, pointerLocked: true, timestamp: 14003 }
Logger.js:102:21
[INFO] Click détecté, demande de verrouillage du pointeur 
Object {  }
Logger.js:102:21
[INFO] État du verrouillage du pointeur changé 
Object { isLocked: true, element: "BODY" }
Logger.js:102:21
[MINING] 🖱️ Mousedown détecté 
Object { button: 0, pointerLocked: true, windowWorldExists: true, playerExists: true, timestamp: 14148 }
Logger.js:102:21
[MINING] ✅ Début du minage (clic gauche) 
Object { button: 0, pointerLocked: true, worldChunks: 481, timestamp: 14148 }
Logger.js:102:21
[MINING] 🔨 startMining appelé 
Object { isMining: false, inventoryOpen: false, worldProvided: true, windowWorldExists: true, timestamp: 14148 }
Logger.js:102:21
[MINING] 🎯 Recherche de bloc cible... 
Object { timestamp: 14148 }
Logger.js:102:21
[MINING] 🎯 getTargetBlock appelé 
Object { worldChunks: 481, cameraPosition: {…}, timestamp: 14148 }
Logger.js:102:21
[DEBUG] Nombre de meshes Ã  tester 
Object { count: 1017 }
Logger.js:102:21
[MINING] 📊 Meshes trouvés pour raycast 
Object { count: 1017, timestamp: 14148 }
Logger.js:102:21
[MINING] 🎯 Direction du raycast 
Object { origin: {…}, direction: {…}, timestamp: 14148 }
Logger.js:102:21
Uncaught TypeError: can't access property "set", this.raycaster is undefined
    getTargetBlock http://localhost:8000/js/player/Player.js?v=1753276301489:913
    startMining http://localhost:8000/js/player/Player.js?v=1753276301489:831
    Controls http://localhost:8000/js/player/Controls.js?v=1753276301489:113
Player.js:913:9
[MINING] Arrêt du minage (relâchement clic) 
Object { button: 0, pointerLocked: true, timestamp: 14814 }
Logger.js:102:21
[INFO] Click détecté, demande de verrouillage du pointeur 
Object {  }
Logger.js:102:21
[INFO] État du verrouillage du pointeur changé 
Object { isLocked: true, element: "BODY" }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 193, cameraPosition: {…}, fps: 60, frameCount: 840 }
Logger.js:102:21
[INFO] Redimensionnement détecté 
Object { oldSize: {…}, newSize: {…}, aspectRatio: "0.806" }
Logger.js:102:21
[DEBUG] Aspect ratio de la camÃ©ra mis Ã  jour 
Object { width: 554, height: 687, aspect: "0.806", fov: 75 }
Logger.js:102:21
[DEBUG] Interface responsive mise à jour 
Object { isSmallScreen: true, isMobile: false, screenType: "small" }
Logger.js:102:21
[INFO] État du verrouillage du pointeur changé 
Object { isLocked: false, element: "BODY" }
Logger.js:102:21
[DEBUG] Statistiques de performance 
Object { fps: 20, chunksGenerated: 504, chunksRendered: 190, chunksInQueue: 0, chunksBeingGenerated: 0 }
Logger.js:102:21
Jeu de règles ignoré suite à un mauvais sélecteur. style.css:480:43
Propriété « -moz-user-drag » inconnue.  Déclaration abandonnée. mining-ui.css:226:20
[DEBUG] État de la scène 
Object { sceneObjects: 193, cameraPosition: {…}, fps: 60, frameCount: 960 }
Logger.js:102:21
[INFO] Redimensionnement détecté 
Object { oldSize: {…}, newSize: {…}, aspectRatio: "0.636" }
Logger.js:102:21
[DEBUG] Aspect ratio de la camÃ©ra mis Ã  jour 
Object { width: 437, height: 687, aspect: "0.636", fov: 75 }
Logger.js:102:21
[DEBUG] Interface responsive mise à jour 
Object { isSmallScreen: true, isMobile: true, screenType: "mobile" }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 193, cameraPosition: {…}, fps: 60, frameCount: 1080 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 193, cameraPosition: {…}, fps: 60, frameCount: 1200 }
Logger.js:102:21
startDiagnostic()
Uncaught ReferenceError: startDiagnostic is not defined
    <anonymous> debugger eval code:1
debugger eval code:1:1
[DEBUG] État de la scène 
Object { sceneObjects: 193, cameraPosition: {…}, fps: 60, frameCount: 1320 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 193, cameraPosition: {…}, fps: 60, frameCount: 1440 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 193, cameraPosition: {…}, fps: 60, frameCount: 1560 }
Logger.js:102:21
testMining()
Uncaught ReferenceError: testMining is not defined
    <anonymous> debugger eval code:1
debugger eval code:1:1
[DEBUG] État de la scène 
Object { sceneObjects: 193, cameraPosition: {…}, fps: 60, frameCount: 1680 }
Logger.js:102:21
