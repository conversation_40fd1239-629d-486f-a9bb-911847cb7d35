Scripts "build/three.js" and "build/three.min.js" are deprecated with r150+, and will be removed with r160. Please use ES Modules or alternatives: https://threejs.org/docs/index.html#manual/en/introduction/Installation three.min.js:1:9
🔄 CACHE BUSTER ACTIVÉ - Timestamp: 1753269174751 localhost:8000:171:17
🎨 CSS chargé avec cache-buster: css/style.css?v=1753269174751 localhost:8000:187:21
📦 Script chargé avec cache-buster: http://localhost:8000/js/version.js?v=1753269174751 localhost:8000:179:21
📦 Script chargé avec cache-buster: http://localhost:8000/js/utils/SmartLogger.js?v=1753269174751 localhost:8000:179:21
📦 Script chargé avec cache-buster: http://localhost:8000/js/utils/SimplexNoise.js?v=1753269174751 localhost:8000:179:21
📦 Script chargé avec cache-buster: http://localhost:8000/js/main.js?v=1753269174751 localhost:8000:179:21
Jeu de règles ignoré suite à un mauvais sélecteur. style.css:480:43
🚀 ===== MINECRAFT JS - v1753269174826 ===== version.js:25:17
📅 Dernière mise à jour: 2025-07-23T11:12:54.826Z version.js:26:17
🔄 Cache Buster: 1753269174826 version.js:27:17
📦 Modules chargés: version.js:28:17
   ✅ PLAYER: Player-v1753269174826 version.js:30:21
   ✅ WORLD: World-v1753269174826 version.js:30:21
   ✅ CHUNK: Chunk-v1753269174826 version.js:30:21
   ✅ CONTROLS: Controls-v1753269174826 version.js:30:21
   ✅ MAIN: Main-v1753269174826 version.js:30:21
🛡️ Protection anti-cache: ACTIVÉE version.js:32:17
=============================================== version.js:33:17
🛡️ Auto-reload DÉSACTIVÉ pour éviter les boucles infinies version.js:45:17
🚀 ===== MINECRAFT JS - v1753269174896 ===== version.js:25:17
📅 Dernière mise à jour: 2025-07-23T11:12:54.896Z version.js:26:17
🔄 Cache Buster: 1753269174896 version.js:27:17
📦 Modules chargés: version.js:28:17
   ✅ PLAYER: Player-v1753269174896 version.js:30:21
   ✅ WORLD: World-v1753269174896 version.js:30:21
   ✅ CHUNK: Chunk-v1753269174896 version.js:30:21
   ✅ CONTROLS: Controls-v1753269174896 version.js:30:21
   ✅ MAIN: Main-v1753269174896 version.js:30:21
🛡️ Protection anti-cache: ACTIVÉE version.js:32:17
=============================================== version.js:33:17
🛡️ Auto-reload DÉSACTIVÉ pour éviter les boucles infinies 2 version.js:45:17
📦 Import avec cache-buster: ./utils/Logger.js?v=1753269174896 main.js:10:13
[SYSTEM] Logger initialized 
Object { sessionId: "session-2025-07-23T11-12-54-930Z-kolf2h", timestamp: "2025-07-23T11:12:54.931Z", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", url: "http://localhost:8000/" }
Logger.js:102:21
🔒 Sauvegarde automatique configurée - Seulement avant fermeture ou erreur critique Logger.js:175:17
🔍 Logger initialisé - Session: session-2025-07-23T11-12-54-930Z-kolf2h Logger.js:19:17
📦 Import avec cache-buster: ./utils/TextureGenerator.js?v=1753269174896 main.js:10:13
📦 Import avec cache-buster: ./world/World.js?v=1753269174896 main.js:10:13
📦 Import avec cache-buster: ./player/Player.js?v=1753269174896 main.js:10:13
📦 Import avec cache-buster: ./player/Controls.js?v=1753269174896 main.js:10:13
📦 Import avec cache-buster: ./ui/OptionsManager.js?v=1753269174896 main.js:10:13
[INFO] Tous les modules importés avec succès 
Object { version: "v1753269174896", cacheBuster: 1753269174896 }
Logger.js:102:21
[INFO] Three.js chargé 
Object { revision: "160" }
Logger.js:102:21
[INFO] Modules de génération de terrain chargés 
Object {  }
Logger.js:102:21
[INFO] Renderer créé 
Object { size: {…}, pixelRatio: 1, canvas: "game" }
Logger.js:102:21
[INFO] Création du générateur de textures... 
Object {  }
Logger.js:102:21
[INFO] TextureGenerator initialisé 
Object { blockTypes: 15, textureSize: 16 }
Logger.js:102:21
[INFO] Générateur de textures créé 
Object {  }
Logger.js:102:21
[INFO] Création du monde... 
Object {  }
Logger.js:102:21
[INFO] Système de monde chargé 
Object { version: "World-v1753269175233", features: {…} }
Logger.js:102:21
[INFO] Génération des chunks initiaux... 
Object {  }
Logger.js:102:21
[CHUNK] Préparation des chunks initiaux 
Object { totalChunks: 25, radius: 2, batchSize: 5, timestamp: 833 }
Logger.js:102:21
[INFO] Génération des chunks initiaux... 
Object {  }
Logger.js:102:21
[CHUNK] Préparation des chunks initiaux 
Object { totalChunks: 25, radius: 2, batchSize: 5, timestamp: 835 }
Logger.js:102:21
[INFO] Monde créé 
Object { chunks: 0 }
Logger.js:102:21
[INFO] Création du joueur... 
Object {  }
Logger.js:102:21
[INFO] Player initialisÃ© 
Object { version: "Player-v1753269175241", features: {…} }
Logger.js:102:21
[INFO] CamÃ©ra initialisÃ©e 
Object { position: {…}, rotation: {…} }
Logger.js:102:21
[INFO] Joueur créé 
Object { position: {…}, flyMode: false }
Logger.js:102:21
[INFO] Création des contrôles... 
Object {  }
Logger.js:102:21
[INFO] Contrôles initialisés 
Object { sensitivity: 0.002, domElement: "BODY" }
Logger.js:102:21
[INFO] Contrôles créés 
Object { keys: 0 }
Logger.js:102:21
[INFO] Création du gestionnaire d'options... 
Object {  }
Logger.js:102:21
[DEBUG] Paramètres de couleur appliqués 
Object { filters: (8) […], hdrMode: false, colorCorrection: true }
Logger.js:102:21
[DEBUG] Mode de couleur appliqué 
Object { mode: "classic", filters: (3) […] }
Logger.js:102:21
[DEBUG] Paramètres de couleur appliqués 
Object { filters: (8) […], hdrMode: false, colorCorrection: true }
Logger.js:102:21
[INFO] OptionsManager initialisé 
Object { settings: {…} }
Logger.js:102:21
[INFO] Gestionnaire d'options créé 
Object {  }
Logger.js:102:21
[INFO] Éclairage ajouté 
Object { sceneObjects: 3, lights: 2 }
Logger.js:102:21
[DEBUG] Interface responsive mise à jour 
Object { isSmallScreen: false, isMobile: false, screenType: "desktop" }
Logger.js:102:21
[INFO] Système de redimensionnement responsif initialisé 
Object { initialSize: {…}, pixelRatio: 1, responsive: true }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 842 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 100 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Chunk manquant détecté: (-6, 0), distance: 6.00 
Object { timestamp: 843 }
Logger.js:102:21
[CHUNK] Chunk manquant détecté: (-5, -3), distance: 5.83 
Object { timestamp: 843 }
Logger.js:102:21
[CHUNK] Chunk manquant détecté: (-5, 3), distance: 5.83 
Object { timestamp: 843 }
Logger.js:102:21
[CHUNK] Chunk manquant détecté: (-3, -5), distance: 5.83 
Object { timestamp: 843 }
Logger.js:102:21
[CHUNK] Chunk manquant détecté: (-3, 5), distance: 5.83 
Object { timestamp: 843 }
Logger.js:102:21
[CHUNK] Chunk manquant détecté: (0, -6), distance: 6.00 
Object { timestamp: 844 }
Logger.js:102:21
[CHUNK] Chunk manquant détecté: (0, 6), distance: 6.00 
Object { timestamp: 844 }
Logger.js:102:21
[CHUNK] Chunk manquant détecté: (3, -5), distance: 5.83 
Object { timestamp: 844 }
Logger.js:102:21
[CHUNK] Chunk manquant détecté: (3, 5), distance: 5.83 
Object { timestamp: 844 }
Logger.js:102:21
[CHUNK] Chunk manquant détecté: (4, 4), distance: 5.66 
Object { timestamp: 844 }
Logger.js:102:21
[CHUNK] Chunk manquant détecté: (5, -3), distance: 5.83 
Object { timestamp: 844 }
Logger.js:102:21
[CHUNK] Chunk manquant détecté: (5, 3), distance: 5.83 
Object { timestamp: 844 }
Logger.js:102:21
[CHUNK] Chunk manquant détecté: (6, 0), distance: 6.00 
Object { timestamp: 844 }
Logger.js:102:21
[CHUNK] 13 chunks manquants détectés et ajoutés à la file 
Object { timestamp: 844 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 112, timestamp: 845 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 111, timestamp: 845 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 110, timestamp: 845 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 3, cameraPosition: {…}, fps: 63, frameCount: 0 }
Logger.js:102:21
[INFO] Protection contre la sélection de texte activée avec surveillance dynamique 
Object {  }
Logger.js:102:21
[INFO] Système de secours initialisé 
Object { shortcuts: {…} }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 848 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 100 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 853 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.9993152 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 862 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.996819136 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 109, timestamp: 863 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 108, timestamp: 863 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 866 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.99334636 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 876 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.98887196 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 879 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.983511064 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 107, timestamp: 880 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 106, timestamp: 881 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 890 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.977148472 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 893 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.969801496 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 900 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.961437896 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 105, timestamp: 902 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 104, timestamp: 902 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 908 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.952254472 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 914 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.942036016 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 921 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.930693168 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 103, timestamp: 922 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 102, timestamp: 923 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 928 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.918674448 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 934 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.9055634 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 942 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.89148908 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 101, timestamp: 943 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 100, timestamp: 944 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 949 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.876451488 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 956 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.860404512 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 962 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.8434376 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 99, timestamp: 963 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 98, timestamp: 964 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 970 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.82550741600001 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 976 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.806559512 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 984 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.78670000800001 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 97, timestamp: 985 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 96, timestamp: 985 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 990 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.76587723200001 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 998 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.74409118400001 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1004 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.72127630400001 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 95, timestamp: 1005 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 94, timestamp: 1005 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1011 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.69756093600002 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1019 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.67266893600002 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1025 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.64701869600002 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 93, timestamp: 1026 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 92, timestamp: 1027 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1032 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.62063527200002 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1039 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.59298736800001 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1046 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.564452864 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 91, timestamp: 1050 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 90, timestamp: 1050 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1053 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.534955088 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1060 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.50449404 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1067 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.47261692 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 89, timestamp: 1067 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 88, timestamp: 1068 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1074 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.440588952 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1082 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.407235312 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1088 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.3729184 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 87, timestamp: 1089 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 86, timestamp: 1089 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1095 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.337638216 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1101 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.301290312 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1108 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.264080808 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 85, timestamp: 1110 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 84, timestamp: 1110 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1116 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.22601804 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1123 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.186320888 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1129 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.146672832 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 83, timestamp: 1130 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 82, timestamp: 1131 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1136 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.10549190399999 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1143 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.06346326399999 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1150 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.02047135199999 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 81, timestamp: 1151 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 80, timestamp: 1152 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1158 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 98.97651616799999 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1164 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 98.93107991999999 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1171 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 98.885187088 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 79, timestamp: 1172 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 78, timestamp: 1172 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1177 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 98.83873607999999 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1185 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 98.79092503199999 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1191 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 98.742150712 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 77, timestamp: 1193 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 76, timestamp: 1193 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1198 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 98.692269784 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1205 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 98.641127784 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1212 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 98.58945254400001 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 75, timestamp: 1213 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 74, timestamp: 1213 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1220 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 98.53726912 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1227 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 98.483675664 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1233 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 98.428961712 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 73, timestamp: 1234 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 72, timestamp: 1234 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1240 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 98.373438936 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1248 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 98.316952888 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1254 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 98.259503568 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 71, timestamp: 1255 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 70, timestamp: 1256 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1261 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 98.201090976 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1268 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 98.141544 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1275 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 98.081202088 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 69, timestamp: 1276 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 68, timestamp: 1276 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1282 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 98.01989690399999 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1289 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 97.957628448 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1296 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 97.89439671999999 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 67, timestamp: 1297 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 66, timestamp: 1297 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1302 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 97.83001671999999 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1309 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 97.76485567199998 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1317 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 97.69873135199998 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 65, timestamp: 1318 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 64, timestamp: 1318 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1324 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 97.63164375999997 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1330 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 97.56359289599997 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1338 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 97.49437987199997 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 63, timestamp: 1339 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 62, timestamp: 1340 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1345 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 97.42439968799997 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
Matériaux de base configurés avec succès! Chunk.js:188:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "0,-1", position: {…}, blocksCount: 32768, timestamp: 1399 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "0,-1", position: {…}, blocksCount: 32768, timestamp: 1412 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1412 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 97.35345623199997 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: (1) […] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1418 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 3, visible: true, timestamp: 1434 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "0,1", position: {…}, blocksCount: 32768, timestamp: 1450 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "0,1", position: {…}, blocksCount: 32768, timestamp: 1461 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1462 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 96.70588123199997 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: (3) […] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 61, timestamp: 1463 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 60, timestamp: 1464 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "1,0", position: {…}, blocksCount: 32768, timestamp: 1480 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "1,0", position: {…}, blocksCount: 32768, timestamp: 1490 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-1,0", position: {…}, blocksCount: 32768, timestamp: 1501 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-1,0", position: {…}, blocksCount: 32768, timestamp: 1511 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "0,0", position: {…}, blocksCount: 32768, timestamp: 1523 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "0,0", position: {…}, blocksCount: 32768, timestamp: 1532 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 5, timestamp: 1533 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 5, timestamp: 1533 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 1533 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 96.14157691199996 }
Logger.js:102:21
[INFO] Spawn rÃ©ussi 
Object { spawnPosition: {…}, groundHeight: 68, chunkKey: "0,0" }
Logger.js:102:21
[PHYSICS] Positionnement initial rÃ©ussi 
Object { finalPosition: {…}, physicsEnabled: true, timestamp: 1534 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1629 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 12, visible: true, timestamp: 1637 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1640 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 13, visible: true, timestamp: 1650 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-1,1", position: {…}, blocksCount: 32768, timestamp: 1660 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-1,1", position: {…}, blocksCount: 32768, timestamp: 1669 }
Logger.js:102:21
[INFO] Click détecté, demande de verrouillage du pointeur 
Object {  }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1672 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 16, visible: true, timestamp: 1682 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1683 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 17, visible: true, timestamp: 1692 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "1,-1", position: {…}, blocksCount: 32768, timestamp: 1702 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "1,-1", position: {…}, blocksCount: 32768, timestamp: 1710 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1725 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 20, visible: true, timestamp: 1735 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-2,0", position: {…}, blocksCount: 32768, timestamp: 1745 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-2,0", position: {…}, blocksCount: 32768, timestamp: 1753 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 10, timestamp: 1754 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 10, timestamp: 1754 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1756 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 23, visible: true, timestamp: 1765 }
Logger.js:102:21
[INFO] État du verrouillage du pointeur changé 
Object { isLocked: true, element: "BODY" }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1767 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 24, visible: true, timestamp: 1777 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-2,-1", position: {…}, blocksCount: 32768, timestamp: 1787 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-2,-1", position: {…}, blocksCount: 32768, timestamp: 1796 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 59, timestamp: 1797 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 58, timestamp: 1798 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1814 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 27, visible: true, timestamp: 1823 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "0,-2", position: {…}, blocksCount: 32768, timestamp: 1834 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "0,-2", position: {…}, blocksCount: 32768, timestamp: 1843 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1845 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 30, visible: true, timestamp: 1855 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-2,1", position: {…}, blocksCount: 32768, timestamp: 1865 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-2,1", position: {…}, blocksCount: 32768, timestamp: 1876 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 15, timestamp: 1876 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 15, timestamp: 1876 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1878 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 33, visible: true, timestamp: 1887 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1910 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 34, visible: true, timestamp: 1919 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1921 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 35, visible: true, timestamp: 1937 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "1,-2", position: {…}, blocksCount: 32768, timestamp: 1946 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "1,-2", position: {…}, blocksCount: 32768, timestamp: 1955 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1956 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 38, visible: true, timestamp: 1967 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "1,2", position: {…}, blocksCount: 32768, timestamp: 1976 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "1,2", position: {…}, blocksCount: 32768, timestamp: 1986 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2000 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 41, visible: true, timestamp: 2009 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,-1", position: {…}, blocksCount: 32768, timestamp: 2019 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,-1", position: {…}, blocksCount: 32768, timestamp: 2028 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 20, timestamp: 2029 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 20, timestamp: 2029 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2030 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 44, visible: true, timestamp: 2040 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2042 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 45, visible: true, timestamp: 2050 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2052 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 46, visible: true, timestamp: 2062 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2064 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 47, visible: true, timestamp: 2074 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,-2", position: {…}, blocksCount: 32768, timestamp: 2083 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,-2", position: {…}, blocksCount: 32768, timestamp: 2092 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 2092 }
Logger.js:102:21
[CHUNK] Chunk manquant détecté: (0, -7), distance: 6.00 
Object { timestamp: 2093 }
Logger.js:102:21
[CHUNK] Chunk manquant détecté: (6, -1), distance: 6.00 
Object { timestamp: 2093 }
Logger.js:102:21
[CHUNK] 2 chunks manquants détectés et ajoutés à la file 
Object { timestamp: 2093 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 101, timestamp: 2093 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 100, timestamp: 2093 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 99, timestamp: 2093 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2118 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 50, visible: true, timestamp: 2128 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,2", position: {…}, blocksCount: 32768, timestamp: 2139 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,2", position: {…}, blocksCount: 32768, timestamp: 2149 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 25, timestamp: 2149 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 25, timestamp: 2150 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2152 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 53, visible: true, timestamp: 2161 }
Logger.js:102:21
[INFO] Génération de chunks initiaux terminée 
Object { totalGenerated: 53, totalRendered: 53 }
Logger.js:102:21
[INFO] Génération de chunks initiaux terminée 
Object { totalGenerated: 53, totalRendered: 53 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2163 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 54, visible: true, timestamp: 2174 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2176 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 55, visible: true, timestamp: 2186 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2202 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 56, visible: true, timestamp: 2213 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2215 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 57, visible: true, timestamp: 2227 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2229 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 58, visible: true, timestamp: 2239 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2241 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 59, visible: true, timestamp: 2251 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2253 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 60, visible: true, timestamp: 2265 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2266 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 61, visible: true, timestamp: 2276 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2295 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 62, visible: true, timestamp: 2305 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2307 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 63, visible: true, timestamp: 2318 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2319 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 64, visible: true, timestamp: 2331 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2333 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 65, visible: true, timestamp: 2344 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2345 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 66, visible: true, timestamp: 2364 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 98, timestamp: 2365 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 97, timestamp: 2365 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2378 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 67, visible: true, timestamp: 2388 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2390 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 68, visible: true, timestamp: 2399 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2401 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 69, visible: true, timestamp: 2411 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2412 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 70, visible: true, timestamp: 2424 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2427 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 71, visible: true, timestamp: 2438 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2440 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 72, visible: true, timestamp: 2452 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2468 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 73, visible: true, timestamp: 2479 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2481 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 74, visible: true, timestamp: 2490 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2493 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 75, visible: true, timestamp: 2503 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2505 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 76, visible: true, timestamp: 2516 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2517 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 77, visible: true, timestamp: 2528 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2530 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 78, visible: true, timestamp: 2542 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2558 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 79, visible: true, timestamp: 2569 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2571 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 80, visible: true, timestamp: 2581 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2583 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 81, visible: true, timestamp: 2597 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2598 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 82, visible: true, timestamp: 2608 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2609 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 83, visible: true, timestamp: 2620 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2621 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 84, visible: true, timestamp: 2631 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 96, timestamp: 2631 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 95, timestamp: 2632 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2649 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 85, visible: true, timestamp: 2659 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2660 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 86, visible: true, timestamp: 2671 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2673 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 87, visible: true, timestamp: 2682 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2683 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 88, visible: true, timestamp: 2692 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2694 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 89, visible: true, timestamp: 2703 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 94, timestamp: 2723 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 93, timestamp: 2723 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2744 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 90, visible: true, timestamp: 2753 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 92, timestamp: 2754 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 91, timestamp: 2754 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2762 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 91, visible: true, timestamp: 2772 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2780 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 92, visible: true, timestamp: 2789 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2790 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 93, visible: true, timestamp: 2801 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 90, timestamp: 2808 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 89, timestamp: 2808 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2828 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 94, visible: true, timestamp: 2839 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 88, timestamp: 2840 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 87, timestamp: 2840 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2855 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 95, visible: true, timestamp: 2865 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2873 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 96, visible: true, timestamp: 2884 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2891 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 97, visible: true, timestamp: 2903 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 86, timestamp: 2904 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 85, timestamp: 2904 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 84, timestamp: 2933 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 83, timestamp: 2934 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2948 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 98, visible: true, timestamp: 2962 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2972 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 99, visible: true, timestamp: 2983 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 2985 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 100, visible: true, timestamp: 2997 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 3008 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 101, visible: true, timestamp: 3019 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 82, timestamp: 3021 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 81, timestamp: 3021 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 3046 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 102, visible: true, timestamp: 3060 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 80, timestamp: 3060 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 79, timestamp: 3060 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 3073 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 103, visible: true, timestamp: 3089 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 3100 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 104, visible: true, timestamp: 3111 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 3112 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 105, visible: true, timestamp: 3124 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 78, timestamp: 3146 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 77, timestamp: 3146 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 3173 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 106, visible: true, timestamp: 3184 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 76, timestamp: 3185 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 75, timestamp: 3185 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 3199 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 107, visible: true, timestamp: 3213 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 3227 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 108, visible: true, timestamp: 3239 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 3242 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 109, visible: true, timestamp: 3259 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 74, timestamp: 3270 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 73, timestamp: 3270 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 112, cameraPosition: {…}, fps: 72, frameCount: 120 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 72, timestamp: 3301 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 71, timestamp: 3303 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 3315 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 110, visible: true, timestamp: 3327 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 3338 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 111, visible: true, timestamp: 3349 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 3350 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 112, visible: true, timestamp: 3361 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 3378 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 3379 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 70, timestamp: 3380 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 69, timestamp: 3380 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 3406 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 3406 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 3409 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 113, visible: true, timestamp: 3420 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 68, timestamp: 3421 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 67, timestamp: 3421 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 3449 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 114, visible: true, timestamp: 3461 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 3476 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 115, visible: true, timestamp: 3488 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 66, timestamp: 3489 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 65, timestamp: 3489 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 3513 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 116, visible: true, timestamp: 3525 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 3535 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 117, visible: true, timestamp: 3545 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 64, timestamp: 3546 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 63, timestamp: 3546 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 3564 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 118, visible: true, timestamp: 3576 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 3588 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 3588 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 62, timestamp: 3589 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 61, timestamp: 3590 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 60, timestamp: 3621 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 59, timestamp: 3621 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 3631 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 3631 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 3633 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 119, visible: true, timestamp: 3643 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 3652 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 120, visible: true, timestamp: 3662 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 3663 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 121, visible: true, timestamp: 3674 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 58, timestamp: 3683 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 57, timestamp: 3683 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 3721 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 3722 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 3723 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 122, visible: true, timestamp: 3733 }
Logger.js:102:21
[MINING] Début du minage (clic gauche) 
Object { button: 0, pointerLocked: true, timestamp: 3908 }
Logger.js:102:21
[DEBUG] getTargetBlock appelÃ© 
Object {  }
Logger.js:102:21
[DEBUG] Nombre de meshes Ã  tester 
Object { count: 857 }
Logger.js:102:21
[DEBUG] Direction du raycast 
Object { origin: {…}, direction: {…} }
Logger.js:102:21
Uncaught TypeError: can't access property "set", this.raycaster is undefined
    getTargetBlock http://localhost:8000/js/player/Player.js?v=1753269174896:857
    startMining http://localhost:8000/js/player/Player.js?v=1753269174896:802
    Controls http://localhost:8000/js/player/Controls.js?v=1753269174896:100
    Controls http://localhost:8000/js/player/Controls.js?v=1753269174896:92
    <anonymous> http://localhost:8000/js/main.js?v=1753269174751:85
Player.js:857:9
[MINING] Arrêt du minage (relâchement clic) 
Object { button: 0, pointerLocked: true, timestamp: 4038 }
Logger.js:102:21
[INFO] Click détecté, demande de verrouillage du pointeur 
Object {  }
Logger.js:102:21
[INFO] État du verrouillage du pointeur changé 
Object { isLocked: true, element: "BODY" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 27, timestamp: 4148 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4169 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 123, visible: true, timestamp: 4182 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 25, timestamp: 4192 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4221 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 124, visible: true, timestamp: 4231 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 23, timestamp: 4232 }
Logger.js:102:21
[MINING] Début du minage (clic gauche) 
Object { button: 0, pointerLocked: true, timestamp: 4264 }
Logger.js:102:21
[DEBUG] getTargetBlock appelÃ© 
Object {  }
Logger.js:102:21
[DEBUG] Nombre de meshes Ã  tester 
Object { count: 875 }
Logger.js:102:21
[DEBUG] Direction du raycast 
Object { origin: {…}, direction: {…} }
Logger.js:102:21
Uncaught TypeError: can't access property "set", this.raycaster is undefined
    getTargetBlock http://localhost:8000/js/player/Player.js?v=1753269174896:857
    startMining http://localhost:8000/js/player/Player.js?v=1753269174896:802
    Controls http://localhost:8000/js/player/Controls.js?v=1753269174896:100
    Controls http://localhost:8000/js/player/Controls.js?v=1753269174896:92
    <anonymous> http://localhost:8000/js/main.js?v=1753269174751:85
Player.js:857:9
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4266 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 125, visible: true, timestamp: 4279 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 22, timestamp: 4279 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 21, timestamp: 4279 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4303 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 126, visible: true, timestamp: 4314 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4323 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 127, visible: true, timestamp: 4338 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 20, timestamp: 4338 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 19, timestamp: 4339 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4368 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 128, visible: true, timestamp: 4378 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 18, timestamp: 4378 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 17, timestamp: 4379 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4388 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 129, visible: true, timestamp: 4400 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4409 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 130, visible: true, timestamp: 4421 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4423 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 131, visible: true, timestamp: 4436 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 16, timestamp: 4447 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 15, timestamp: 4447 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4478 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 132, visible: true, timestamp: 4490 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.8284271247461903, queueRemaining: 14, timestamp: 4491 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 13, timestamp: 4492 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4505 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 133, visible: true, timestamp: 4521 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4532 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 4533 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4535 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 134, visible: true, timestamp: 4547 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 12, timestamp: 4556 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 11, timestamp: 4556 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 10, timestamp: 4579 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 9, timestamp: 4579 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4593 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 4593 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4596 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 135, visible: true, timestamp: 4607 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4616 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 136, visible: true, timestamp: 4627 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4629 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 4629 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 8, timestamp: 4636 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 7, timestamp: 4636 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4664 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 137, visible: true, timestamp: 4675 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 6, timestamp: 4675 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 3, queueRemaining: 5, timestamp: 4676 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4689 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 4689 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4697 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 4698 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4699 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 4700 }
Logger.js:102:21
[MINING] Arrêt du minage (relâchement clic) 
Object { button: 0, pointerLocked: true, timestamp: 4707 }
Logger.js:102:21
[INFO] Click détecté, demande de verrouillage du pointeur 
Object {  }
Logger.js:102:21
[INFO] État du verrouillage du pointeur changé 
Object { isLocked: true, element: "BODY" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 3, queueRemaining: 4, timestamp: 4708 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 3, queueRemaining: 3, timestamp: 4708 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4736 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 138, visible: true, timestamp: 4747 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 3, queueRemaining: 2, timestamp: 4748 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 3, queueRemaining: 1, timestamp: 4748 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4759 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 4760 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4771 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 4772 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4774 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 139, visible: true, timestamp: 4784 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 3, queueRemaining: 0, timestamp: 4793 }
Logger.js:102:21
[MINING] Début du minage (clic gauche) 
Object { button: 0, pointerLocked: true, timestamp: 4815 }
Logger.js:102:21
[DEBUG] getTargetBlock appelÃ© 
Object {  }
Logger.js:102:21
[DEBUG] Nombre de meshes Ã  tester 
Object { count: 1010 }
Logger.js:102:21
[DEBUG] Direction du raycast 
Object { origin: {…}, direction: {…} }
Logger.js:102:21
Uncaught TypeError: can't access property "set", this.raycaster is undefined
    getTargetBlock http://localhost:8000/js/player/Player.js?v=1753269174896:857
    startMining http://localhost:8000/js/player/Player.js?v=1753269174896:802
    Controls http://localhost:8000/js/player/Controls.js?v=1753269174896:100
    Controls http://localhost:8000/js/player/Controls.js?v=1753269174896:92
    <anonymous> http://localhost:8000/js/main.js?v=1753269174751:85
Player.js:857:9
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 4827 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 140, visible: true, timestamp: 4838 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 143, cameraPosition: {…}, fps: 144, frameCount: 240 }
Logger.js:102:21
[MINING] Arrêt du minage (relâchement clic) 
Object { button: 0, pointerLocked: true, timestamp: 4960 }
Logger.js:102:21
[INFO] Click détecté, demande de verrouillage du pointeur 
Object {  }
Logger.js:102:21
[INFO] État du verrouillage du pointeur changé 
Object { isLocked: true, element: "BODY" }
Logger.js:102:21
[MINING] Début du minage (clic gauche) 
Object { button: 0, pointerLocked: true, timestamp: 5039 }
Logger.js:102:21
[DEBUG] getTargetBlock appelÃ© 
Object {  }
Logger.js:102:21
[DEBUG] Nombre de meshes Ã  tester 
Object { count: 1021 }
Logger.js:102:21
[DEBUG] Direction du raycast 
Object { origin: {…}, direction: {…} }
Logger.js:102:21
Uncaught TypeError: can't access property "set", this.raycaster is undefined
    getTargetBlock http://localhost:8000/js/player/Player.js?v=1753269174896:857
    startMining http://localhost:8000/js/player/Player.js?v=1753269174896:802
    Controls http://localhost:8000/js/player/Controls.js?v=1753269174896:100
    Controls http://localhost:8000/js/player/Controls.js?v=1753269174896:92
    <anonymous> http://localhost:8000/js/main.js?v=1753269174751:85
Player.js:857:9
[MINING] Arrêt du minage (relâchement clic) 
Object { button: 0, pointerLocked: true, timestamp: 5176 }
Logger.js:102:21
[INFO] Click détecté, demande de verrouillage du pointeur 
Object {  }
Logger.js:102:21
[INFO] État du verrouillage du pointeur changé 
Object { isLocked: true, element: "BODY" }
Logger.js:102:21
[MINING] Début du minage (clic gauche) 
Object { button: 0, pointerLocked: true, timestamp: 5220 }
Logger.js:102:21
[DEBUG] getTargetBlock appelÃ© 
Object {  }
Logger.js:102:21
[DEBUG] Nombre de meshes Ã  tester 
Object { count: 1021 }
Logger.js:102:21
[DEBUG] Direction du raycast 
Object { origin: {…}, direction: {…} }
Logger.js:102:21
Uncaught TypeError: can't access property "set", this.raycaster is undefined
    getTargetBlock http://localhost:8000/js/player/Player.js?v=1753269174896:857
    startMining http://localhost:8000/js/player/Player.js?v=1753269174896:802
    Controls http://localhost:8000/js/player/Controls.js?v=1753269174896:100
    Controls http://localhost:8000/js/player/Controls.js?v=1753269174896:92
    <anonymous> http://localhost:8000/js/main.js?v=1753269174751:85
Player.js:857:9
[MINING] Arrêt du minage (relâchement clic) 
Object { button: 0, pointerLocked: true, timestamp: 5350 }
Logger.js:102:21
[INFO] Click détecté, demande de verrouillage du pointeur 
Object {  }
Logger.js:102:21
[INFO] État du verrouillage du pointeur changé 
Object { isLocked: true, element: "BODY" }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 143, cameraPosition: {…}, fps: 144, frameCount: 360 }
Logger.js:102:21
[MINING] Début du minage (clic gauche) 
Object { button: 0, pointerLocked: true, timestamp: 6518 }
Logger.js:102:21
[DEBUG] getTargetBlock appelÃ© 
Object {  }
Logger.js:102:21
[DEBUG] Nombre de meshes Ã  tester 
Object { count: 1021 }
Logger.js:102:21
[DEBUG] Direction du raycast 
Object { origin: {…}, direction: {…} }
Logger.js:102:21
Uncaught TypeError: can't access property "set", this.raycaster is undefined
    getTargetBlock http://localhost:8000/js/player/Player.js?v=1753269174896:857
    startMining http://localhost:8000/js/player/Player.js?v=1753269174896:802
    Controls http://localhost:8000/js/player/Controls.js?v=1753269174896:100
    Controls http://localhost:8000/js/player/Controls.js?v=1753269174896:92
    <anonymous> http://localhost:8000/js/main.js?v=1753269174751:85
Player.js:857:9
[MINING] Arrêt du minage (relâchement clic) 
Object { button: 0, pointerLocked: true, timestamp: 6837 }
Logger.js:102:21
[INFO] Click détecté, demande de verrouillage du pointeur 
Object {  }
Logger.js:102:21
[INFO] État du verrouillage du pointeur changé 
Object { isLocked: true, element: "BODY" }
Logger.js:102:21
[MINING] Début du minage (clic gauche) 
Object { button: 0, pointerLocked: true, timestamp: 6947 }
Logger.js:102:21
[DEBUG] getTargetBlock appelÃ© 
Object {  }
Logger.js:102:21
[DEBUG] Nombre de meshes Ã  tester 
Object { count: 1021 }
Logger.js:102:21
[DEBUG] Direction du raycast 
Object { origin: {…}, direction: {…} }
Logger.js:102:21
Uncaught TypeError: can't access property "set", this.raycaster is undefined
    getTargetBlock http://localhost:8000/js/player/Player.js?v=1753269174896:857
    startMining http://localhost:8000/js/player/Player.js?v=1753269174896:802
    Controls http://localhost:8000/js/player/Controls.js?v=1753269174896:100
    Controls http://localhost:8000/js/player/Controls.js?v=1753269174896:92
    <anonymous> http://localhost:8000/js/main.js?v=1753269174751:85
Player.js:857:9
[DEBUG] État de la scène 
Object { sceneObjects: 143, cameraPosition: {…}, fps: 144, frameCount: 480 }
Logger.js:102:21
[MINING] Arrêt du minage (relâchement clic) 
Object { button: 0, pointerLocked: true, timestamp: 7569 }
Logger.js:102:21
[INFO] Click détecté, demande de verrouillage du pointeur 
Object {  }
Logger.js:102:21
[INFO] État du verrouillage du pointeur changé 
Object { isLocked: true, element: "BODY" }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 143, cameraPosition: {…}, fps: 72, frameCount: 600 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 143, cameraPosition: {…}, fps: 72, frameCount: 720 }
Logger.js:102:21
[INFO] Redimensionnement détecté 
Object { oldSize: {…}, newSize: {…}, aspectRatio: "2.027" }
Logger.js:102:21
[DEBUG] Aspect ratio de la camÃ©ra mis Ã  jour 
Object { width: 1920, height: 947, aspect: "2.027", fov: 75 }
Logger.js:102:21
[DEBUG] Interface responsive mise à jour 
Object { isSmallScreen: false, isMobile: false, screenType: "desktop" }
Logger.js:102:21
[INFO] Redimensionnement détecté 
Object { oldSize: {…}, newSize: {…}, aspectRatio: "1.798" }
Logger.js:102:21
[DEBUG] Aspect ratio de la camÃ©ra mis Ã  jour 
Object { width: 1703, height: 947, aspect: "1.798", fov: 75 }
Logger.js:102:21
[DEBUG] Interface responsive mise à jour 
Object { isSmallScreen: false, isMobile: false, screenType: "desktop" }
Logger.js:102:21
[INFO] État du verrouillage du pointeur changé 
Object { isLocked: false, element: "BODY" }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 143, cameraPosition: {…}, fps: 72, frameCount: 840 }
Logger.js:102:21
[DEBUG] Statistiques de performance 
Object { fps: 29, chunksGenerated: 153, chunksRendered: 140, chunksInQueue: 0, chunksBeingGenerated: 0 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 143, cameraPosition: {…}, fps: 144, frameCount: 960 }
Logger.js:102:21
