📄 Logs sauvegardés: minecraft-js-logs-session-2025-07-23T11-02-38-468Z-bt4jmk.html (1000 entrées) Logger.js:193:17
GET
http://localhost:8000/
[HTTP/1 304 Not Modified 3ms]

[INFO] État du verrouillage du pointeur changé 
Object { isLocked: true, element: "BODY" }
Logger.js:102:21
GET
http://localhost:8000/css/style.css
[HTTP/1 200 OK 0ms]

GET
http://localhost:8000/css/mining-ui.css
[HTTP/1 200 OK 0ms]

GET
https://unpkg.com/three@0.160.0/build/three.min.js
[HTTP/2 200  0ms]

Scripts "build/three.js" and "build/three.min.js" are deprecated with r150+, and will be removed with r160. Please use ES Modules or alternatives: https://threejs.org/docs/index.html#manual/en/introduction/Installation three.min.js:1:9
🔄 CACHE BUSTER ACTIVÉ - Timestamp: 1753268574832 localhost:8000:171:17
🎨 CSS chargé avec cache-buster: css/style.css?v=1753268574832 localhost:8000:187:21
📦 Script chargé avec cache-buster: http://localhost:8000/js/version.js?v=1753268574832 localhost:8000:179:21
📦 Script chargé avec cache-buster: http://localhost:8000/js/utils/SmartLogger.js?v=1753268574832 localhost:8000:179:21
📦 Script chargé avec cache-buster: http://localhost:8000/js/utils/SimplexNoise.js?v=1753268574832 localhost:8000:179:21
📦 Script chargé avec cache-buster: http://localhost:8000/js/main.js?v=1753268574832 localhost:8000:179:21
GET
http://localhost:8000/css/style.css?v=1753268574832
[HTTP/1 200 OK 3ms]

GET
http://localhost:8000/js/utils/SmartLogger.js?v=1753268574832
[HTTP/1 200 OK 4ms]

GET
http://localhost:8000/js/version.js?v=1753268574832
[HTTP/1 200 OK 5ms]

GET
http://localhost:8000/js/utils/SimplexNoise.js?v=1753268574832
[HTTP/1 200 OK 5ms]

GET
http://localhost:8000/js/main.js?v=1753268574832
[HTTP/1 200 OK 2ms]

Jeu de règles ignoré suite à un mauvais sélecteur. style.css:480:43
🚀 ===== MINECRAFT JS - v1753268575001 ===== version.js:25:17
📅 Dernière mise à jour: 2025-07-23T11:02:55.001Z version.js:26:17
🔄 Cache Buster: 1753268575001 version.js:27:17
📦 Modules chargés: version.js:28:17
   ✅ PLAYER: Player-v1753268575001 version.js:30:21
   ✅ WORLD: World-v1753268575001 version.js:30:21
   ✅ CHUNK: Chunk-v1753268575001 version.js:30:21
   ✅ CONTROLS: Controls-v1753268575001 version.js:30:21
   ✅ MAIN: Main-v1753268575001 version.js:30:21
🛡️ Protection anti-cache: ACTIVÉE version.js:32:17
=============================================== version.js:33:17
🛡️ Auto-reload DÉSACTIVÉ pour éviter les boucles infinies version.js:45:17
GET
http://localhost:8000/js/version.js
[HTTP/1 200 OK 0ms]

GET
http://localhost:8000/favicon.ico
[HTTP/1 404 File not found 0ms]

🚀 ===== MINECRAFT JS - v1753268575049 ===== version.js:25:17
📅 Dernière mise à jour: 2025-07-23T11:02:55.049Z version.js:26:17
🔄 Cache Buster: 1753268575049 version.js:27:17
📦 Modules chargés: version.js:28:17
   ✅ PLAYER: Player-v1753268575049 version.js:30:21
   ✅ WORLD: World-v1753268575049 version.js:30:21
   ✅ CHUNK: Chunk-v1753268575049 version.js:30:21
   ✅ CONTROLS: Controls-v1753268575049 version.js:30:21
   ✅ MAIN: Main-v1753268575049 version.js:30:21
🛡️ Protection anti-cache: ACTIVÉE version.js:32:17
=============================================== version.js:33:17
🛡️ Auto-reload DÉSACTIVÉ pour éviter les boucles infinies 2 version.js:45:17
📦 Import avec cache-buster: ./utils/Logger.js?v=1753268575049 main.js:10:13
GET
http://localhost:8000/js/utils/Logger.js?v=1753268575049
[HTTP/1 200 OK 3ms]

[SYSTEM] Logger initialized 
Object { sessionId: "session-2025-07-23T11-02-55-215Z-3kq208", timestamp: "2025-07-23T11:02:55.215Z", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", url: "http://localhost:8000/" }
Logger.js:102:21
🔍 Logger initialisé - Session: session-2025-07-23T11-02-55-215Z-3kq208 Logger.js:19:17
📦 Import avec cache-buster: ./utils/TextureGenerator.js?v=1753268575049 main.js:10:13
GET
http://localhost:8000/js/utils/TextureGenerator.js?v=1753268575049
[HTTP/1 200 OK 3ms]

📦 Import avec cache-buster: ./world/World.js?v=1753268575049 main.js:10:13
GET
http://localhost:8000/js/world/World.js?v=1753268575049
[HTTP/1 200 OK 1ms]

GET
http://localhost:8000/js/world/Chunk.js
[HTTP/1 200 OK 0ms]

GET
http://localhost:8000/js/world/WorldGenerator.js
[HTTP/1 200 OK 0ms]

GET
http://localhost:8000/js/utils/WorkerManager.js
[HTTP/1 200 OK 0ms]

📦 Import avec cache-buster: ./player/Player.js?v=1753268575049 main.js:10:13
GET
http://localhost:8000/js/player/Player.js?v=1753268575049
[HTTP/1 200 OK 2ms]

📦 Import avec cache-buster: ./player/Controls.js?v=1753268575049 main.js:10:13
GET
http://localhost:8000/js/player/Controls.js?v=1753268575049
[HTTP/1 200 OK 4ms]

📦 Import avec cache-buster: ./ui/OptionsManager.js?v=1753268575049 main.js:10:13
GET
http://localhost:8000/js/ui/OptionsManager.js?v=1753268575049
[HTTP/1 200 OK 2ms]

[INFO] Tous les modules importés avec succès 
Object { version: "v1753268575049", cacheBuster: 1753268575049 }
Logger.js:102:21
[INFO] Three.js chargé 
Object { revision: "160" }
Logger.js:102:21
[INFO] Modules de génération de terrain chargés 
Object {  }
Logger.js:102:21
[INFO] Renderer créé 
Object { size: {…}, pixelRatio: 1, canvas: "game" }
Logger.js:102:21
[INFO] Création du générateur de textures... 
Object {  }
Logger.js:102:21
[INFO] TextureGenerator initialisé 
Object { blockTypes: 15, textureSize: 16 }
Logger.js:102:21
[INFO] Générateur de textures créé 
Object {  }
Logger.js:102:21
[INFO] Création du monde... 
Object {  }
Logger.js:102:21
[INFO] Système de monde chargé 
Object { version: "World-v1753268575368", features: {…} }
Logger.js:102:21
[INFO] Génération des chunks initiaux... 
Object {  }
Logger.js:102:21
[CHUNK] Préparation des chunks initiaux 
Object { totalChunks: 25, radius: 2, batchSize: 5, timestamp: 5915 }
Logger.js:102:21
[INFO] Génération des chunks initiaux... 
Object {  }
Logger.js:102:21
[CHUNK] Préparation des chunks initiaux 
Object { totalChunks: 25, radius: 2, batchSize: 5, timestamp: 5916 }
Logger.js:102:21
[INFO] Monde créé 
Object { chunks: 0 }
Logger.js:102:21
[INFO] Création du joueur... 
Object {  }
Logger.js:102:21
[INFO] Player initialisÃ© 
Object { version: "Player-v1753268575374", features: {…} }
Logger.js:102:21
[INFO] CamÃ©ra initialisÃ©e 
Object { position: {…}, rotation: {…} }
Logger.js:102:21
[INFO] Joueur créé 
Object { position: {…}, flyMode: false }
Logger.js:102:21
[INFO] Création des contrôles... 
Object {  }
Logger.js:102:21
[INFO] Contrôles initialisés 
Object { sensitivity: 0.002, domElement: "BODY" }
Logger.js:102:21
[INFO] Contrôles créés 
Object { keys: 0 }
Logger.js:102:21
[INFO] Création du gestionnaire d'options... 
Object {  }
Logger.js:102:21
[DEBUG] Paramètres de couleur appliqués 
Object { filters: (8) […], hdrMode: false, colorCorrection: true }
Logger.js:102:21
[DEBUG] Mode de couleur appliqué 
Object { mode: "classic", filters: (3) […] }
Logger.js:102:21
[DEBUG] Paramètres de couleur appliqués 
Object { filters: (8) […], hdrMode: false, colorCorrection: true }
Logger.js:102:21
[INFO] OptionsManager initialisé 
Object { settings: {…} }
Logger.js:102:21
[INFO] Gestionnaire d'options créé 
Object {  }
Logger.js:102:21
[INFO] Éclairage ajouté 
Object { sceneObjects: 3, lights: 2 }
Logger.js:102:21
[DEBUG] Interface responsive mise à jour 
Object { isSmallScreen: false, isMobile: false, screenType: "desktop" }
Logger.js:102:21
[INFO] Système de redimensionnement responsif initialisé 
Object { initialSize: {…}, pixelRatio: 1, responsive: true }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 5924 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 100 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Chunk manquant détecté: (-6, 0), distance: 6.00 
Object { timestamp: 5925 }
Logger.js:102:21
[CHUNK] Chunk manquant détecté: (-5, -3), distance: 5.83 
Object { timestamp: 5925 }
Logger.js:102:21
[CHUNK] Chunk manquant détecté: (-5, 3), distance: 5.83 
Object { timestamp: 5925 }
Logger.js:102:21
[CHUNK] Chunk manquant détecté: (-3, -5), distance: 5.83 
Object { timestamp: 5925 }
Logger.js:102:21
[CHUNK] Chunk manquant détecté: (-3, 5), distance: 5.83 
Object { timestamp: 5926 }
Logger.js:102:21
[CHUNK] Chunk manquant détecté: (0, -6), distance: 6.00 
Object { timestamp: 5926 }
Logger.js:102:21
[CHUNK] Chunk manquant détecté: (0, 6), distance: 6.00 
Object { timestamp: 5926 }
Logger.js:102:21
[CHUNK] Chunk manquant détecté: (3, -5), distance: 5.83 
Object { timestamp: 5926 }
Logger.js:102:21
[CHUNK] Chunk manquant détecté: (3, 5), distance: 5.83 
Object { timestamp: 5926 }
Logger.js:102:21
[CHUNK] Chunk manquant détecté: (4, 4), distance: 5.66 
Object { timestamp: 5926 }
Logger.js:102:21
[CHUNK] Chunk manquant détecté: (5, -3), distance: 5.83 
Object { timestamp: 5926 }
Logger.js:102:21
[CHUNK] Chunk manquant détecté: (5, 3), distance: 5.83 
Object { timestamp: 5926 }
Logger.js:102:21
[CHUNK] Chunk manquant détecté: (6, 0), distance: 6.00 
Object { timestamp: 5926 }
Logger.js:102:21
[CHUNK] 13 chunks manquants détectés et ajoutés à la file 
Object { timestamp: 5926 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 112, timestamp: 5927 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 111, timestamp: 5927 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 110, timestamp: 5927 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 3, cameraPosition: {…}, fps: 63, frameCount: 0 }
Logger.js:102:21
[INFO] Protection contre la sélection de texte activée avec surveillance dynamique 
Object {  }
Logger.js:102:21
[INFO] Système de secours initialisé 
Object { shortcuts: {…} }
Logger.js:102:21
[INFO] Click détecté, demande de verrouillage du pointeur 
Object {  }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 5930 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 100 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 5935 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.998816 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 5945 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.99606588 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 109, timestamp: 5946 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 108, timestamp: 5946 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 5949 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.992362696 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 5959 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.987723136 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 5963 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.982082752 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 107, timestamp: 5963 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 106, timestamp: 5965 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 5973 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.97549252799999 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[INFO] État du verrouillage du pointeur changé 
Object { isLocked: true, element: "BODY" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 5977 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.96793903199999 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 5984 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.95942226399998 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 105, timestamp: 5985 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 104, timestamp: 5986 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 5991 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.94994222399998 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 5997 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.93949891199998 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 6005 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.92805945599997 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 103, timestamp: 6005 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 102, timestamp: 6005 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 6011 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.91568682399998 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 0ms]

GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 0ms]

GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 0ms]

GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 0ms]

[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 6018 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.90235091999998 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 0ms]

GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 0ms]

GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 0ms]

GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 0ms]

GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 0ms]

[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 6025 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.88805174399998 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 0ms]

[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 101, timestamp: 6026 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 100, timestamp: 6027 }
Logger.js:102:21
GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 0ms]

GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 0ms]

[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 6032 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.87278929599998 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 6039 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.85651681599998 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 6046 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.83907732799999 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 99, timestamp: 6047 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 98, timestamp: 6047 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 6053 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.82090840799998 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 6060 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.80205189599998 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 6067 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.78197031199998 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 97, timestamp: 6069 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 96, timestamp: 6069 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 6074 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.76062221599999 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 6081 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.73885406399998 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 6088 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.71587988799998 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 95, timestamp: 6089 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 94, timestamp: 6090 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 6094 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.69194243999998 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 6101 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.66696995999997 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 6109 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.64110319199997 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 93, timestamp: 6110 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 92, timestamp: 6111 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 6117 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.61427315199998 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 6145 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.58647983999998 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 6161 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.47137063199999 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 91, timestamp: 6163 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 90, timestamp: 6163 }
Logger.js:102:21
Matériaux de base configurés avec succès! Chunk.js:188:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "0,-1", position: {…}, blocksCount: 32768, timestamp: 6196 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "0,-1", position: {…}, blocksCount: 32768, timestamp: 6209 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 6209 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.40614573599998 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: (1) […] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-1,0", position: {…}, blocksCount: 32768, timestamp: 6229 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-1,0", position: {…}, blocksCount: 32768, timestamp: 6241 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "0,1", position: {…}, blocksCount: 32768, timestamp: 6254 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "0,1", position: {…}, blocksCount: 32768, timestamp: 6267 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 6268 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.16417371999998 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore gÃ©nÃ©rÃ© 
Object { chunkKey: "0,0", availableChunks: (3) […] }
Logger.js:102:21
[WARN] Positionnement initial Ã©chouÃ© 
Object { reason: "Chunk pas encore gÃ©nÃ©rÃ©", action: "Attente de gÃ©nÃ©ration des chunks" }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6359 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 7, visible: true, timestamp: 6369 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "0,0", position: {…}, blocksCount: 32768, timestamp: 6382 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "0,0", position: {…}, blocksCount: 32768, timestamp: 6392 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6393 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 10, visible: true, timestamp: 6405 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6407 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 11, visible: true, timestamp: 6420 }
Logger.js:102:21
[PHYSICS] DÃ©but du positionnement initial 
Object { timestamp: 6420 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 98.83375515199998 }
Logger.js:102:21
[INFO] Spawn rÃ©ussi 
Object { spawnPosition: {…}, groundHeight: 68, chunkKey: "0,0" }
Logger.js:102:21
[PHYSICS] Positionnement initial rÃ©ussi 
Object { finalPosition: {…}, physicsEnabled: true, timestamp: 6421 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 89, timestamp: 6422 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 88, timestamp: 6422 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6443 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 12, visible: true, timestamp: 6453 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "1,0", position: {…}, blocksCount: 32768, timestamp: 6467 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "1,0", position: {…}, blocksCount: 32768, timestamp: 6477 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 5, timestamp: 6477 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 5, timestamp: 6477 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6479 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 15, visible: true, timestamp: 6490 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6492 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 16, visible: true, timestamp: 6503 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6521 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 17, visible: true, timestamp: 6535 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 10, timestamp: 6536 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 10, timestamp: 6536 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6537 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 18, visible: true, timestamp: 6547 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6548 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 19, visible: true, timestamp: 6557 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6558 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 20, visible: true, timestamp: 6567 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 15, timestamp: 6567 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 15, timestamp: 6567 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6568 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 21, visible: true, timestamp: 6577 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6579 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 22, visible: true, timestamp: 6587 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6601 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 23, visible: true, timestamp: 6609 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6611 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 24, visible: true, timestamp: 6620 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "1,2", position: {…}, blocksCount: 32768, timestamp: 6628 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "1,2", position: {…}, blocksCount: 32768, timestamp: 6637 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6638 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 27, visible: true, timestamp: 6646 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,-1", position: {…}, blocksCount: 32768, timestamp: 6655 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,-1", position: {…}, blocksCount: 32768, timestamp: 6664 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 20, timestamp: 6664 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 20, timestamp: 6664 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6665 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 30, visible: true, timestamp: 6673 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 87, timestamp: 6673 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 86, timestamp: 6674 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6694 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 31, visible: true, timestamp: 6703 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6705 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 32, visible: true, timestamp: 6713 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-2,2", position: {…}, blocksCount: 32768, timestamp: 6723 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-2,2", position: {…}, blocksCount: 32768, timestamp: 6733 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6734 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 35, visible: true, timestamp: 6743 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,-2", position: {…}, blocksCount: 32768, timestamp: 6752 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,-2", position: {…}, blocksCount: 32768, timestamp: 6760 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6780 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 38, visible: true, timestamp: 6789 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,2", position: {…}, blocksCount: 32768, timestamp: 6799 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,2", position: {…}, blocksCount: 32768, timestamp: 6807 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 25, timestamp: 6808 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 25, timestamp: 6808 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6810 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 41, visible: true, timestamp: 6819 }
Logger.js:102:21
[INFO] Génération de chunks initiaux terminée 
Object { totalGenerated: 41, totalRendered: 41 }
Logger.js:102:21
[INFO] Génération de chunks initiaux terminée 
Object { totalGenerated: 41, totalRendered: 41 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6820 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 42, visible: true, timestamp: 6829 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 85, timestamp: 6841 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 84, timestamp: 6841 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6863 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 43, visible: true, timestamp: 6874 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 83, timestamp: 6875 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 82, timestamp: 6875 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6881 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 44, visible: true, timestamp: 6891 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6898 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 45, visible: true, timestamp: 6910 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6916 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 46, visible: true, timestamp: 6925 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 81, timestamp: 6926 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 80, timestamp: 6926 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 79, timestamp: 6946 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 78, timestamp: 6946 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6951 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 47, visible: true, timestamp: 6963 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6970 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 48, visible: true, timestamp: 6979 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6981 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 49, visible: true, timestamp: 6990 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 6998 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 50, visible: true, timestamp: 7008 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 77, timestamp: 7009 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 76, timestamp: 7009 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7027 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 51, visible: true, timestamp: 7038 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 75, timestamp: 7039 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 74, timestamp: 7039 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7046 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 52, visible: true, timestamp: 7060 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7067 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 53, visible: true, timestamp: 7076 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7078 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 54, visible: true, timestamp: 7088 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 73, timestamp: 7096 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 72, timestamp: 7097 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7116 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 55, visible: true, timestamp: 7127 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 71, timestamp: 7127 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 70, timestamp: 7128 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7134 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 56, visible: true, timestamp: 7145 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7153 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 57, visible: true, timestamp: 7165 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7172 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 58, visible: true, timestamp: 7181 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 69, timestamp: 7182 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 68, timestamp: 7182 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7197 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 59, visible: true, timestamp: 7207 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7214 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 60, visible: true, timestamp: 7224 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 67, timestamp: 7225 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 66, timestamp: 7225 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7243 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 61, visible: true, timestamp: 7252 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7260 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 62, visible: true, timestamp: 7269 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 7271 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 99, timestamp: 7272 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 98, timestamp: 7272 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7292 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 63, visible: true, timestamp: 7304 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 97, timestamp: 7304 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 96, timestamp: 7305 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7313 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 64, visible: true, timestamp: 7327 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7333 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 65, visible: true, timestamp: 7344 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7345 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 66, visible: true, timestamp: 7356 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 95, timestamp: 7366 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 94, timestamp: 7367 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7384 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 67, visible: true, timestamp: 7394 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7401 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 68, visible: true, timestamp: 7411 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 93, timestamp: 7411 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 92, timestamp: 7412 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7428 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 69, visible: true, timestamp: 7437 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 91, timestamp: 7438 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 90, timestamp: 7438 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7445 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 70, visible: true, timestamp: 7455 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7461 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 71, visible: true, timestamp: 7471 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7477 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 72, visible: true, timestamp: 7487 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 89, timestamp: 7487 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 88, timestamp: 7487 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7505 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 73, visible: true, timestamp: 7515 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 87, timestamp: 7516 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 86, timestamp: 7516 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7522 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 74, visible: true, timestamp: 7534 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7541 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 75, visible: true, timestamp: 7549 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7550 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 76, visible: true, timestamp: 7559 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 85, timestamp: 7565 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 84, timestamp: 7566 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 83, timestamp: 7584 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 82, timestamp: 7584 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7591 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 77, visible: true, timestamp: 7607 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7614 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 78, visible: true, timestamp: 7623 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7625 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 79, visible: true, timestamp: 7633 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7642 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 80, visible: true, timestamp: 7650 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 7651 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 99, timestamp: 7651 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 98, timestamp: 7652 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7664 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 81, visible: true, timestamp: 7673 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7680 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 82, visible: true, timestamp: 7690 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 97, timestamp: 7690 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 96, timestamp: 7690 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7707 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 83, visible: true, timestamp: 7717 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 95, timestamp: 7717 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 94, timestamp: 7718 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7726 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 84, visible: true, timestamp: 7735 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7742 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 85, visible: true, timestamp: 7751 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7758 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 86, visible: true, timestamp: 7766 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 93, timestamp: 7767 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 92, timestamp: 7767 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7784 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 87, visible: true, timestamp: 7795 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 91, timestamp: 7797 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 90, timestamp: 7798 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7805 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 88, visible: true, timestamp: 7814 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7822 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 89, visible: true, timestamp: 7831 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7838 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 90, visible: true, timestamp: 7848 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 89, timestamp: 7849 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 88, timestamp: 7849 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7867 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7867 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7868 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 91, visible: true, timestamp: 7877 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 87, timestamp: 7878 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 86, timestamp: 7878 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7895 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7896 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7897 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 92, visible: true, timestamp: 7908 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 85, timestamp: 7919 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 84, timestamp: 7919 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7934 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7934 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7941 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 7941 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 83, timestamp: 7942 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 82, timestamp: 7942 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 95, cameraPosition: {…}, fps: 146, frameCount: 120 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7963 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 93, visible: true, timestamp: 7973 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 81, timestamp: 7974 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 80, timestamp: 7974 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 7981 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 94, visible: true, timestamp: 7994 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8003 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 95, visible: true, timestamp: 8015 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8022 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 96, visible: true, timestamp: 8031 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 79, timestamp: 8032 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 78, timestamp: 8032 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8052 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8052 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8054 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 97, visible: true, timestamp: 8066 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 77, timestamp: 8067 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 76, timestamp: 8068 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 75, timestamp: 8094 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 74, timestamp: 8094 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8101 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8102 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8110 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 98, visible: true, timestamp: 8122 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8132 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 99, visible: true, timestamp: 8143 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 73, timestamp: 8144 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 72, timestamp: 8144 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8158 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 100, visible: true, timestamp: 8170 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8178 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8179 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8180 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 101, visible: true, timestamp: 8189 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 71, timestamp: 8197 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 70, timestamp: 8198 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8217 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8217 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8219 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 102, visible: true, timestamp: 8230 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 69, timestamp: 8231 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 68, timestamp: 8231 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8248 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8249 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8255 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8255 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 67, timestamp: 8256 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 66, timestamp: 8256 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8274 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 103, visible: true, timestamp: 8285 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8294 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 104, visible: true, timestamp: 8304 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 65, timestamp: 8304 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.8284271247461903, queueRemaining: 64, timestamp: 8304 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8327 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8328 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 63, timestamp: 8329 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 62, timestamp: 8329 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8351 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 105, visible: true, timestamp: 8362 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8363 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 106, visible: true, timestamp: 8374 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8385 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 107, visible: true, timestamp: 8398 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 61, timestamp: 8398 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 60, timestamp: 8398 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 59, timestamp: 8420 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 58, timestamp: 8420 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8430 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8431 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8433 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 108, visible: true, timestamp: 8447 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8459 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 109, visible: true, timestamp: 8469 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8471 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8471 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 57, timestamp: 8485 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 56, timestamp: 8485 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8510 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8510 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8511 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8511 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 3, queueRemaining: 55, timestamp: 8512 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 3, queueRemaining: 54, timestamp: 8513 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8538 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 110, visible: true, timestamp: 8548 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 3, queueRemaining: 53, timestamp: 8549 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 3, queueRemaining: 52, timestamp: 8549 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8558 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 111, visible: true, timestamp: 8567 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8578 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8578 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8580 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8580 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 51, timestamp: 8587 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 50, timestamp: 8587 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8604 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 112, visible: true, timestamp: 8613 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8623 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 113, visible: true, timestamp: 8632 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 49, timestamp: 8632 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 48, timestamp: 8632 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8651 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 114, visible: true, timestamp: 8661 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8670 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 115, visible: true, timestamp: 8679 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 47, timestamp: 8679 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 46, timestamp: 8680 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8696 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8697 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8699 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 116, visible: true, timestamp: 8708 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 45, timestamp: 8716 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 44, timestamp: 8716 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8740 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 117, visible: true, timestamp: 8750 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 43, timestamp: 8751 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 42, timestamp: 8751 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8769 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 118, visible: true, timestamp: 8779 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8790 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8790 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8793 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8793 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 8799 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 99, timestamp: 8896 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 98, timestamp: 8896 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8927 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 130, visible: true, timestamp: 8936 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8945 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 131, visible: true, timestamp: 8955 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 97, timestamp: 8963 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 96, timestamp: 8963 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8980 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 132, visible: true, timestamp: 8989 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 8999 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 8999 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 95, timestamp: 9000 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 94, timestamp: 9000 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9016 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9017 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9018 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9018 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 93, timestamp: 9027 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 92, timestamp: 9028 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9051 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9051 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9053 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9054 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 91, timestamp: 9063 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 90, timestamp: 9063 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9084 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9084 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9086 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9086 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 89, timestamp: 9095 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 88, timestamp: 9095 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9122 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9123 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9125 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9126 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 9126 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 99, timestamp: 9185 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 98, timestamp: 9185 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9210 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9211 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9212 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 139, visible: true, timestamp: 9223 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 97, timestamp: 9245 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 96, timestamp: 9246 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9271 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 140, visible: true, timestamp: 9281 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9292 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 141, visible: true, timestamp: 9304 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 95, timestamp: 9305 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 94, timestamp: 9305 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9330 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 142, visible: true, timestamp: 9342 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9353 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9353 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 93, timestamp: 9354 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 92, timestamp: 9354 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9375 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9376 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9377 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 143, visible: true, timestamp: 9388 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 91, timestamp: 9398 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 90, timestamp: 9398 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9429 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9429 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9431 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 144, visible: true, timestamp: 9441 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 89, timestamp: 9441 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 88, timestamp: 9441 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9462 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9463 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9474 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9474 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 87, timestamp: 9474 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 86, timestamp: 9475 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9497 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9498 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9507 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9508 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 85, timestamp: 9508 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 84, timestamp: 9509 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9526 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9526 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9534 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9534 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 83, timestamp: 9535 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 82, timestamp: 9535 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9554 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9554 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9563 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9564 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 81, timestamp: 9564 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 80, timestamp: 9564 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9587 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9588 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9597 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9598 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 79, timestamp: 9598 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 78, timestamp: 9598 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9627 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9627 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9629 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9629 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 77, timestamp: 9630 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 76, timestamp: 9630 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9650 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9650 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9660 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9660 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 75, timestamp: 9660 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 74, timestamp: 9661 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9682 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9682 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9683 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9683 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 73, timestamp: 9693 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 72, timestamp: 9693 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 147, cameraPosition: {…}, fps: 72, frameCount: 240 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9717 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9717 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9718 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9719 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 71, timestamp: 9728 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 70, timestamp: 9729 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9749 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9749 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9750 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9751 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 69, timestamp: 9762 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 68, timestamp: 9762 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9789 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9790 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 67, timestamp: 9790 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 66, timestamp: 9791 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9800 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 145, visible: true, timestamp: 9810 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9820 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9821 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9821 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9822 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 65, timestamp: 9829 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 64, timestamp: 9830 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9850 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9850 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9852 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9853 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 63, timestamp: 9860 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 62, timestamp: 9861 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9884 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9885 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9886 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9887 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 61, timestamp: 9896 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 60, timestamp: 9896 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9920 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9922 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9933 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9934 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 59, timestamp: 9934 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 58, timestamp: 9934 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9956 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9957 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9967 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9968 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 57, timestamp: 9968 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 56, timestamp: 9968 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9988 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9989 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 9992 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 9992 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 55, timestamp: 10000 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 54, timestamp: 10000 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10025 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10025 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10034 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10034 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 53, timestamp: 10035 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 52, timestamp: 10035 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10055 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10055 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10056 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10057 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 51, timestamp: 10064 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 50, timestamp: 10064 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10087 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10087 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10088 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10089 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 49, timestamp: 10099 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 48, timestamp: 10099 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10123 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10123 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10133 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10134 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 47, timestamp: 10134 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 46, timestamp: 10134 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10152 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10152 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10153 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10154 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 45, timestamp: 10161 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 44, timestamp: 10161 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10180 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10180 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10181 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10181 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 43, timestamp: 10189 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 42, timestamp: 10190 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10211 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10212 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10214 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10215 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 41, timestamp: 10222 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 40, timestamp: 10222 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10245 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10245 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10247 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10247 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 39, timestamp: 10255 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 38, timestamp: 10256 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10278 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10279 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10280 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10280 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 37, timestamp: 10289 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 36, timestamp: 10289 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10316 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10316 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10328 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10328 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 35, timestamp: 10329 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 34, timestamp: 10330 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10360 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10361 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10362 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10363 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 33, timestamp: 10363 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 32, timestamp: 10363 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10391 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10391 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10393 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10393 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 31, timestamp: 10393 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 30, timestamp: 10394 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10411 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10411 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 29, timestamp: 10418 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 28, timestamp: 10419 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10431 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10431 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10439 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10439 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10441 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10441 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 27, timestamp: 10451 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 26, timestamp: 10452 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10468 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10468 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10470 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10471 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 25, timestamp: 10480 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 24, timestamp: 10480 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10503 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10504 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10505 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10506 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 23, timestamp: 10507 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 22, timestamp: 10507 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10533 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10534 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10535 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10536 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 21, timestamp: 10536 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 20, timestamp: 10536 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10565 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10566 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10568 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10569 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 19, timestamp: 10570 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 18, timestamp: 10570 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 17, timestamp: 10601 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 16, timestamp: 10601 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10610 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10611 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10613 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10613 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10625 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10625 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10627 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10627 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 15, timestamp: 10635 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 14, timestamp: 10635 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 13, timestamp: 10663 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 12, timestamp: 10663 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10680 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10681 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10684 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10685 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10701 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10701 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10703 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10703 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 11, timestamp: 10713 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 10, timestamp: 10713 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10735 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10735 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10744 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10744 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 9, timestamp: 10745 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 8, timestamp: 10745 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10770 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10771 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10787 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10788 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 7, timestamp: 10788 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 6, timestamp: 10789 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10821 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10822 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10824 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10824 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 5, timestamp: 10835 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 4, timestamp: 10835 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10860 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10860 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10873 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10874 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 3, timestamp: 10874 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 2, timestamp: 10875 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10900 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10901 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10913 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10914 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 1, timestamp: 10915 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 0, timestamp: 10916 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10953 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10953 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 10966 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 10966 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 148, cameraPosition: {…}, fps: 72, frameCount: 360 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 148, cameraPosition: {…}, fps: 144, frameCount: 480 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 148, cameraPosition: {…}, fps: 141, frameCount: 600 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 13041 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 99, timestamp: 13154 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 98, timestamp: 13154 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13196 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13196 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13197 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13197 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 97, timestamp: 13204 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 96, timestamp: 13204 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 12, wasOnGround: true, flyMode: false, cooldownRemaining: 200, jumpStartTime: 1753268582683, timestamp: 13226 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13234 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13235 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13236 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13236 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 95, timestamp: 13237 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 94, timestamp: 13237 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13254 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13254 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13263 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13264 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 93, timestamp: 13268 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 92, timestamp: 13268 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 91, timestamp: 13294 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 90, timestamp: 13294 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13305 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13305 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13307 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13307 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13319 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13319 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13320 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13321 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 89, timestamp: 13331 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 88, timestamp: 13332 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13352 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13353 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13361 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13361 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 87, timestamp: 13362 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 86, timestamp: 13362 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13377 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13378 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13380 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13380 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 85, timestamp: 13387 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 84, timestamp: 13387 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13404 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13405 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13406 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13406 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 83, timestamp: 13412 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 82, timestamp: 13413 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13431 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13432 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13433 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13434 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 81, timestamp: 13442 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 80, timestamp: 13442 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13459 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13459 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13461 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13461 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 79, timestamp: 13468 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 78, timestamp: 13468 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13482 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13483 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13484 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13485 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 77, timestamp: 13492 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 76, timestamp: 13492 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13506 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13507 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13508 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13508 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 75, timestamp: 13515 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 74, timestamp: 13516 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13536 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13536 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13544 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13545 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 73, timestamp: 13545 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 72, timestamp: 13545 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13562 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13563 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13564 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13565 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 71, timestamp: 13571 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 70, timestamp: 13572 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13587 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13588 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13596 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13597 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 69, timestamp: 13597 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 68, timestamp: 13597 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13612 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13613 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13614 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13614 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 67, timestamp: 13622 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 66, timestamp: 13622 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13647 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13647 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 65, timestamp: 13648 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 64, timestamp: 13648 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13666 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13667 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13668 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13668 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13670 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13670 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 63, timestamp: 13677 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 62, timestamp: 13678 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13698 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13698 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13706 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13707 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 61, timestamp: 13707 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 60, timestamp: 13707 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13726 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13726 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13737 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13737 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 59, timestamp: 13738 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 58, timestamp: 13738 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13754 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13754 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13756 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13757 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 13764 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 99, timestamp: 13830 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 98, timestamp: 13831 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13861 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13862 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13863 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13863 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 97, timestamp: 13871 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 96, timestamp: 13871 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13888 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13890 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13892 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13892 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 95, timestamp: 13900 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 94, timestamp: 13901 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13928 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13928 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 93, timestamp: 13929 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 92, timestamp: 13930 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13939 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13939 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13948 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13948 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13959 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13959 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 91, timestamp: 13961 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 90, timestamp: 13962 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13979 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13979 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 13981 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 13981 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 89, timestamp: 13988 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 88, timestamp: 13989 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14012 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14012 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14023 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14023 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 87, timestamp: 14023 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 86, timestamp: 14023 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14044 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14045 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14046 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14047 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 85, timestamp: 14056 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 84, timestamp: 14056 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14074 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14075 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14084 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14085 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 83, timestamp: 14085 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 82, timestamp: 14085 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14105 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14106 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14108 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14108 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 81, timestamp: 14116 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 80, timestamp: 14117 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14142 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14142 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14144 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14144 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 79, timestamp: 14151 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 78, timestamp: 14152 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14169 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14169 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14171 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14171 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 77, timestamp: 14178 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 76, timestamp: 14178 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14195 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14196 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14197 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14197 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 75, timestamp: 14204 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 74, timestamp: 14205 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 73, timestamp: 14231 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 72, timestamp: 14231 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14240 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14240 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14249 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14249 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14251 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14252 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14253 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14254 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 71, timestamp: 14262 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 70, timestamp: 14262 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14279 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14279 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14281 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14281 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 69, timestamp: 14289 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 68, timestamp: 14290 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 67, timestamp: 14319 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 66, timestamp: 14319 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14328 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14329 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14331 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14332 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14343 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14345 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14354 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14355 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 65, timestamp: 14355 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 64, timestamp: 14356 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14376 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14377 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14378 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14378 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 63, timestamp: 14387 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 62, timestamp: 14388 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 169, cameraPosition: {…}, fps: 144, frameCount: 720 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14409 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14409 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14411 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14411 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 61, timestamp: 14423 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 60, timestamp: 14423 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14442 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14443 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14444 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14445 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 59, timestamp: 14452 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 58, timestamp: 14452 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14470 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14470 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14477 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14478 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 57, timestamp: 14478 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 56, timestamp: 14478 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14495 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14495 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14497 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14497 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 55, timestamp: 14504 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 54, timestamp: 14504 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14524 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14524 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14525 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14526 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 53, timestamp: 14533 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 52, timestamp: 14534 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14551 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14551 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14553 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14553 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 51, timestamp: 14560 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 50, timestamp: 14561 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14583 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14583 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14585 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14585 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 49, timestamp: 14592 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 48, timestamp: 14592 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14608 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14609 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14623 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14625 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 47, timestamp: 14626 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 46, timestamp: 14626 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14644 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14644 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14648 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14648 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 45, timestamp: 14655 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 44, timestamp: 14655 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14675 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14676 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14678 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14678 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 43, timestamp: 14687 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 42, timestamp: 14687 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14712 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14713 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 41, timestamp: 14713 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 40, timestamp: 14713 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14730 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14731 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14732 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14733 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14743 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14743 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 39, timestamp: 14751 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 38, timestamp: 14751 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14775 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14776 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 37, timestamp: 14776 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 36, timestamp: 14777 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14789 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14790 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14801 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14802 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14803 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14805 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 14811 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 99, timestamp: 14893 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 98, timestamp: 14894 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14930 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14931 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14933 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14933 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 97, timestamp: 14955 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 96, timestamp: 14955 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14974 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14974 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 14976 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 14977 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 95, timestamp: 14995 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 94, timestamp: 14995 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15017 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15018 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15019 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15019 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 93, timestamp: 15027 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 92, timestamp: 15027 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15045 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15045 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15046 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15047 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 91, timestamp: 15057 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 90, timestamp: 15057 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15076 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15077 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15078 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15079 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 89, timestamp: 15086 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 88, timestamp: 15087 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15116 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15116 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15118 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15119 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 87, timestamp: 15119 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 86, timestamp: 15119 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15133 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15133 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15135 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15135 }
Logger.js:102:21
[DEBUG] Mouvement bloquÃ© par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 85, timestamp: 15153 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 84, timestamp: 15154 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15173 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15174 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15175 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15175 }
Logger.js:102:21
[DEBUG] Mouvement bloquÃ© par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[DEBUG] Mouvement bloquÃ© par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 83, timestamp: 15185 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 82, timestamp: 15185 }
Logger.js:102:21
[DEBUG] Mouvement bloquÃ© par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15207 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15207 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15209 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15209 }
Logger.js:102:21
[DEBUG] Mouvement bloquÃ© par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[DEBUG] Mouvement bloquÃ© par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 81, timestamp: 15218 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 80, timestamp: 15218 }
Logger.js:102:21
[DEBUG] Mouvement bloquÃ© par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15237 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15238 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15239 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15240 }
Logger.js:102:21
[DEBUG] Mouvement bloquÃ© par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[DEBUG] Mouvement bloquÃ© par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 79, timestamp: 15248 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 78, timestamp: 15249 }
Logger.js:102:21
[DEBUG] Mouvement bloquÃ© par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15270 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15271 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15273 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15273 }
Logger.js:102:21
[DEBUG] Mouvement bloquÃ© par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[DEBUG] Mouvement bloquÃ© par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 77, timestamp: 15283 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 76, timestamp: 15283 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15306 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15307 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15308 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15309 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 75, timestamp: 15317 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 74, timestamp: 15318 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15332 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15333 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15335 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15335 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 73, timestamp: 15349 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 72, timestamp: 15350 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15370 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15370 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15371 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15372 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 71, timestamp: 15381 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 70, timestamp: 15381 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15399 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15400 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15401 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15402 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 69, timestamp: 15409 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 68, timestamp: 15409 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15425 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15425 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15427 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15428 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 67, timestamp: 15434 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 66, timestamp: 15434 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15458 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15458 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15460 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15460 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 65, timestamp: 15467 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 64, timestamp: 15468 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15486 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15487 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15488 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15488 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 63, timestamp: 15496 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 62, timestamp: 15497 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15527 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15528 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15529 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15529 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 61, timestamp: 15536 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 60, timestamp: 15536 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15555 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15555 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15556 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15557 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 59, timestamp: 15564 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 58, timestamp: 15565 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15580 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15581 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15591 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15591 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 57, timestamp: 15592 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 56, timestamp: 15592 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15609 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15610 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15611 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15611 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 55, timestamp: 15622 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 54, timestamp: 15622 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15637 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15638 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15639 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15640 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 53, timestamp: 15646 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 52, timestamp: 15647 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15663 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15664 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15674 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15674 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 51, timestamp: 15674 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 50, timestamp: 15674 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15689 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15689 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15691 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15691 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 49, timestamp: 15699 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 48, timestamp: 15699 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15717 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15717 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15718 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15719 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 47, timestamp: 15725 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 46, timestamp: 15726 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 178, cameraPosition: {…}, fps: 144, frameCount: 840 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15744 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15745 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15746 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15747 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 45, timestamp: 15754 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 44, timestamp: 15754 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15773 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15774 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15796 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15796 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 43, timestamp: 15796 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 42, timestamp: 15797 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15813 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15814 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15821 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15822 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 41, timestamp: 15822 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 40, timestamp: 15822 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15841 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15842 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15843 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15843 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 39, timestamp: 15851 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 38, timestamp: 15851 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15898 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15898 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15900 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15900 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 37, timestamp: 15901 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 36, timestamp: 15901 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15923 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15923 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15925 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15925 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 35, timestamp: 15935 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 34, timestamp: 15935 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15954 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15955 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15956 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15957 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 33, timestamp: 15967 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 32, timestamp: 15967 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15989 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 15989 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 15999 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16000 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 31, timestamp: 16000 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 30, timestamp: 16000 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16022 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16022 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16024 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16025 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 29, timestamp: 16035 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 28, timestamp: 16035 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16057 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16057 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16059 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16059 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 27, timestamp: 16066 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 26, timestamp: 16067 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16089 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16090 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16091 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16092 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 25, timestamp: 16109 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 24, timestamp: 16109 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16144 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16145 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16156 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16156 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 23, timestamp: 16157 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 22, timestamp: 16157 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16215 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16216 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16217 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16218 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 21, timestamp: 16226 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 20, timestamp: 16226 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16250 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16251 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16252 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16252 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 19, timestamp: 16263 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 18, timestamp: 16263 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16283 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16284 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16285 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16286 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 17, timestamp: 16294 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 16, timestamp: 16294 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16320 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16320 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16321 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16322 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 15, timestamp: 16330 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 14, timestamp: 16330 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16350 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16350 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16352 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16353 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 13, timestamp: 16360 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 12, timestamp: 16360 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16380 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16380 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16388 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16389 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 11, timestamp: 16389 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 10, timestamp: 16389 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16406 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16406 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16408 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16408 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 9, timestamp: 16414 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 8, timestamp: 16415 }
Logger.js:102:21
[DEBUG] Statistiques de performance 
Object { fps: 40, chunksGenerated: 446, chunksRendered: 175, chunksInQueue: 8, chunksBeingGenerated: 2 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16444 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16445 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16446 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16447 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 7, timestamp: 16447 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 6, timestamp: 16447 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16468 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16468 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16469 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16470 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 5, timestamp: 16477 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 4, timestamp: 16477 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16500 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16500 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16501 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16501 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 3, timestamp: 16502 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 2, timestamp: 16502 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16518 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16519 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16527 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16528 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 1, timestamp: 16528 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 0, timestamp: 16528 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16544 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16544 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16546 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16547 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 16637 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 30, timestamp: 16738 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 29, timestamp: 16739 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16768 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16768 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16776 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16777 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 28, timestamp: 16784 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 27, timestamp: 16784 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16802 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16802 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16803 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16803 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 26, timestamp: 16812 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 25, timestamp: 16812 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16832 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16832 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16833 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16833 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 24, timestamp: 16842 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 23, timestamp: 16842 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16862 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16863 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16865 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16865 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 22, timestamp: 16872 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 21, timestamp: 16872 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16894 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16895 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16897 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16898 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 20, timestamp: 16906 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 19, timestamp: 16906 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16935 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16936 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16937 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16937 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 18, timestamp: 16938 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 17, timestamp: 16938 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16956 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16956 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16968 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16968 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 16, timestamp: 16968 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 15, timestamp: 16968 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16987 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16988 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 16989 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 16990 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 14, timestamp: 16998 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 13, timestamp: 16998 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17019 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17019 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17027 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17028 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 12, timestamp: 17028 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 11, timestamp: 17028 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17048 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17048 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 10, timestamp: 17057 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 9, timestamp: 17057 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17067 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17068 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17079 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17080 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17082 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17082 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 8, timestamp: 17090 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 7, timestamp: 17091 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17110 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17110 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17119 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17120 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 6, timestamp: 17120 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 5, timestamp: 17120 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17142 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17143 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17144 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17144 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 4, timestamp: 17153 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 3, timestamp: 17154 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 190, cameraPosition: {…}, fps: 144, frameCount: 960 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17175 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17176 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17177 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17177 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 2, timestamp: 17185 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 1, timestamp: 17185 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17205 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17206 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17215 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17215 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 0, timestamp: 17216 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17234 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17234 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 17320 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 15, timestamp: 17380 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 14, timestamp: 17380 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17415 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17416 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17418 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17418 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 13, timestamp: 17427 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 12, timestamp: 17428 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17456 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17456 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 11, timestamp: 17457 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 10, timestamp: 17457 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17469 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17469 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17480 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17480 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17482 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17483 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 9, timestamp: 17491 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 8, timestamp: 17491 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17513 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17513 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17514 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17515 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 7, timestamp: 17522 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 6, timestamp: 17523 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17543 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17543 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17545 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17545 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 5, timestamp: 17553 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 4, timestamp: 17553 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17575 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17576 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17585 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17585 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 3, timestamp: 17586 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 2, timestamp: 17586 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17605 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17605 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 1, timestamp: 17612 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 0, timestamp: 17613 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17624 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17625 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17635 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17635 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 12, wasOnGround: true, flyMode: false, cooldownRemaining: 200, jumpStartTime: 1753268587093, timestamp: 17636 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 17646 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 17646 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 196, cameraPosition: {…}, fps: 144, frameCount: 1080 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 18468 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 20, timestamp: 18545 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 19, timestamp: 18546 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 18571 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 18572 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 18574 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 18575 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 18, timestamp: 18590 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 17, timestamp: 18590 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 18609 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 18610 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 18612 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 18612 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 16, timestamp: 18620 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 15, timestamp: 18620 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 18641 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 18642 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 18643 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 18643 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 14, timestamp: 18653 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 13, timestamp: 18653 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 18673 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 18674 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 18675 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 18676 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 12, timestamp: 18683 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 11, timestamp: 18684 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 18704 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 18705 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 18707 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 18707 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 10, timestamp: 18715 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 9, timestamp: 18716 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 18743 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 18744 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 18747 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 18748 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 8, timestamp: 18756 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 7, timestamp: 18757 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 18775 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 18776 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 18784 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 18785 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 6, timestamp: 18785 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 5, timestamp: 18785 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 18802 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 18803 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 18811 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 18812 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 4, timestamp: 18812 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 3, timestamp: 18812 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 18828 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 18828 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 18830 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 18830 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 2, timestamp: 18838 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 1, timestamp: 18839 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 18852 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 18853 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 18862 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 18863 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 0, timestamp: 18870 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 18888 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 18889 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 205, cameraPosition: {…}, fps: 146, frameCount: 1200 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 19534 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 0, timestamp: 19583 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 19610 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 19611 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 20362 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 20, timestamp: 20445 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 19, timestamp: 20446 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 20475 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 20476 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 20477 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 20478 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 18, timestamp: 20493 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 17, timestamp: 20493 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 20514 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 20514 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 20515 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 20515 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 16, timestamp: 20524 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 15, timestamp: 20524 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 20545 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 20546 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 20556 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 20556 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 14, timestamp: 20556 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 13, timestamp: 20556 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 20576 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 20577 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 20578 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 20579 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 12, timestamp: 20588 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 11, timestamp: 20589 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 219, cameraPosition: {…}, fps: 72, frameCount: 1320 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 20612 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 20613 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 20614 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 20615 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 12, wasOnGround: true, flyMode: false, cooldownRemaining: 200, jumpStartTime: 1753268590071, timestamp: 20615 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 10, timestamp: 20624 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 9, timestamp: 20624 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 20646 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 20647 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 20648 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 20649 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 8, timestamp: 20658 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 7, timestamp: 20659 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 20682 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 20682 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 20684 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 20684 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 6, timestamp: 20692 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 5, timestamp: 20693 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 20710 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 20711 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 20713 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 20714 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 4, timestamp: 20723 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 3, timestamp: 20723 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 20744 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 20745 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 20747 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 20747 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 2, timestamp: 20756 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 1, timestamp: 20757 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 20777 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 20778 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 20788 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 20789 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 0, timestamp: 20789 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 20809 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 20810 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 219, cameraPosition: {…}, fps: 147, frameCount: 1440 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 22134 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 20, timestamp: 22246 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 19, timestamp: 22247 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 22278 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 22279 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 22280 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 22281 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 18, timestamp: 22299 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 17, timestamp: 22299 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 22319 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 22319 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 22321 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 22321 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 16, timestamp: 22362 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 15, timestamp: 22362 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 22382 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 22382 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 22391 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 22393 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 14, timestamp: 22393 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 13, timestamp: 22394 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 22413 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 22414 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 22424 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 22425 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 12, timestamp: 22425 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 11, timestamp: 22425 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 22447 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 22448 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 22457 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 22457 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 10, timestamp: 22458 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 9, timestamp: 22458 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 22476 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 22477 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 22486 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 22486 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 8, timestamp: 22486 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 7, timestamp: 22487 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 22503 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 22504 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 22515 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 22515 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 6, timestamp: 22515 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 5, timestamp: 22516 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 22537 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 22537 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 22539 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 22539 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 4, timestamp: 22548 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 3, timestamp: 22549 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 22568 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 22568 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 22577 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 22578 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 2, timestamp: 22578 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 1, timestamp: 22578 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 22600 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 22601 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 22612 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 22613 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 0, timestamp: 22613 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 22634 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 22635 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 231, cameraPosition: {…}, fps: 144, frameCount: 1560 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 12, wasOnGround: true, flyMode: false, cooldownRemaining: 200, jumpStartTime: 1753268592787, timestamp: 23331 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 231, cameraPosition: {…}, fps: 144, frameCount: 1680 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 23904 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 20, timestamp: 24016 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 19, timestamp: 24017 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 24072 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 24073 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 24075 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 24076 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 18, timestamp: 24090 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 17, timestamp: 24090 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 24116 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 24116 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 24119 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 24120 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 16, timestamp: 24132 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 15, timestamp: 24133 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 24151 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 24152 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 24153 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 24154 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 14, timestamp: 24162 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 13, timestamp: 24162 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 24189 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 24190 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 24192 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 24193 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 12, timestamp: 24202 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 11, timestamp: 24202 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 24221 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 24222 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 24223 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 24224 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 10, timestamp: 24232 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 9, timestamp: 24233 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 24262 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 24263 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 8, timestamp: 24263 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 7, timestamp: 24263 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 24272 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 24274 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 24283 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 24283 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 24294 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 24294 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 6, timestamp: 24295 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 5, timestamp: 24295 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 24320 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 24320 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 24321 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 24322 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 4, timestamp: 24330 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 3, timestamp: 24331 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 24354 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 24354 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 24355 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 24356 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 2, timestamp: 24364 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 1, timestamp: 24364 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 24386 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 24386 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 24398 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 24398 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 0, timestamp: 24398 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 24419 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 24419 }
Logger.js:102:21
[DEBUG] Statistiques de performance 
Object { fps: 48, chunksGenerated: 588, chunksRendered: 240, chunksInQueue: 0, chunksBeingGenerated: 0 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 243, cameraPosition: {…}, fps: 144, frameCount: 1800 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 243, cameraPosition: {…}, fps: 144, frameCount: 1920 }
Logger.js:102:21
[INFO] État du verrouillage du pointeur changé 
Object { isLocked: false, element: "BODY" }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 243, cameraPosition: {…}, fps: 144, frameCount: 2040 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 243, cameraPosition: {…}, fps: 144, frameCount: 2160 }
Logger.js:102:21
