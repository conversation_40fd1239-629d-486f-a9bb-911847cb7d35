// Inventory.js - Gestion de l'inventaire et du crafting
export class Inventory {
    constructor() {
        this.items = [];
        this.maxItems = 36;
        this.selectedSlot = 0;
        this.initUI();
    }

    initUI() {
        const inventory = document.getElementById('inventory');
        if (!inventory) return;

        // Créer la grille d'inventaire
        for (let i = 0; i < this.maxItems; i++) {
            const slot = document.createElement('div');
            slot.className = 'inventory-slot';
            slot.dataset.slot = i;
            inventory.appendChild(slot);
        }
    }

    addItem(item) {
        if (this.items.length < this.maxItems) {
            this.items.push(item);
            this.updateUI();
            return true;
        }
        return false;
    }

    removeItem(index) {
        if (index >= 0 && index < this.items.length) {
            this.items.splice(index, 1);
            this.updateUI();
            return true;
        }
        return false;
    }

    updateUI() {
        const slots = document.querySelectorAll('.inventory-slot');
        slots.forEach((slot, index) => {
            slot.innerHTML = '';
            if (this.items[index]) {
                slot.textContent = this.items[index].name;
                slot.style.backgroundColor = '#4a4a4a';
            } else {
                slot.style.backgroundColor = '#2a2a2a';
            }
        });
    }

    selectSlot(index) {
        this.selectedSlot = index;
        document.querySelectorAll('.inventory-slot').forEach((slot, i) => {
            slot.style.border = i === index ? '2px solid white' : '1px solid #666';
        });
    }
} 