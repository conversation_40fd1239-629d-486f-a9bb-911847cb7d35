// Système de logging indépendant pour analyse d'erreurs
export class Logger {
    constructor() {
        this.sessionId = this.generateSessionId();
        this.logs = [];
        this.logIndex = 0;
        this.maxLogs = 1000; // Limite pour éviter la surcharge mémoire
        this.autoSaveInterval = 5000; // Sauvegarde automatique toutes les 5 secondes
        
        // Initialiser le système
        this.init();
        
        // Intercepter les erreurs globales
        this.interceptErrors();
        
        // Démarrer la sauvegarde automatique
        this.startAutoSave();
        
        console.log(`🔍 Logger initialisé - Session: ${this.sessionId}`);
    }
    
    // Générer un ID de session unique
    generateSessionId() {
        const now = new Date();
        const timestamp = now.toISOString().replace(/[:.]/g, '-');
        const random = Math.random().toString(36).substring(2, 8);
        return `session-${timestamp}-${random}`;
    }
    
    // Initialiser le système de logging
    init() {
        this.log('SYSTEM', 'Logger initialized', {
            sessionId: this.sessionId,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        });
    }
    
    // Intercepter les erreurs JavaScript globales
    interceptErrors() {
        // Erreurs JavaScript non capturées
        window.addEventListener('error', (event) => {
            this.log('ERROR', 'JavaScript Error', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error ? event.error.stack : 'No stack trace'
            });
        });
        
        // Promesses rejetées non capturées
        window.addEventListener('unhandledrejection', (event) => {
            this.log('ERROR', 'Unhandled Promise Rejection', {
                reason: event.reason,
                stack: event.reason && event.reason.stack ? event.reason.stack : 'No stack trace'
            });
        });
        
        // Intercepter console.error
        const originalError = console.error;
        console.error = (...args) => {
            this.log('ERROR', 'Console Error', {
                arguments: args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg))
            });
            originalError.apply(console, args);
        };
        
        // Intercepter console.warn
        const originalWarn = console.warn;
        console.warn = (...args) => {
            this.log('WARN', 'Console Warning', {
                arguments: args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg))
            });
            originalWarn.apply(console, args);
        };
    }
    
    // Méthode principale de logging
    log(level, message, data = {}) {
        const logEntry = {
            index: ++this.logIndex,
            sessionId: this.sessionId,
            timestamp: new Date().toISOString(),
            level: level,
            message: message,
            data: data,
            url: window.location.href,
            stackTrace: this.getStackTrace()
        };
        
        this.logs.push(logEntry);
        
        // Limiter le nombre de logs en mémoire
        if (this.logs.length > this.maxLogs) {
            this.logs.shift();
        }
        
        // Log dans la console aussi (si pas d'erreur)
        if (level !== 'ERROR') {
            console.log(`[${level}] ${message}`, data);
        }
    }
    
    // Obtenir la stack trace actuelle
    getStackTrace() {
        try {
            throw new Error();
        } catch (e) {
            return e.stack ? e.stack.split('\n').slice(3, 8).join('\n') : 'No stack trace';
        }
    }
    
    // Méthodes de logging spécialisées
    info(message, data = {}) {
        this.log('INFO', message, data);
    }
    
    warn(message, data = {}) {
        this.log('WARN', message, data);
    }
    
    error(message, data = {}) {
        this.log('ERROR', message, data);
    }
    
    debug(message, data = {}) {
        this.log('DEBUG', message, data);
    }
    
    // Logging spécialisé pour le minage
    mining(action, data = {}) {
        this.log('MINING', action, {
            ...data,
            timestamp: performance.now()
        });
    }
    
    // Logging spécialisé pour la physique
    physics(action, data = {}) {
        this.log('PHYSICS', action, {
            ...data,
            timestamp: performance.now()
        });
    }
    
    // Logging spécialisé pour les chunks
    chunk(action, data = {}) {
        this.log('CHUNK', action, {
            ...data,
            timestamp: performance.now()
        });
    }
    
    // Démarrer la sauvegarde automatique
    startAutoSave() {
        // Ne plus sauvegarder automatiquement toutes les 5 secondes
        // Seulement sauvegarder avant fermeture de la page
        window.addEventListener('beforeunload', () => {
            this.saveToFile();
        });
        
        // Sauvegarder aussi en cas d'erreur critique
        window.addEventListener('error', () => {
            // Attendre un peu pour que l'erreur soit loggée
            setTimeout(() => {
                this.saveToFile();
            }, 100);
        });
    }
    
    // Sauvegarder les logs dans un fichier HTML
    saveToFile() {
        if (this.logs.length === 0) return;
        
        const html = this.generateLogHTML();
        const blob = new Blob([html], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        
        // Créer un lien de téléchargement invisible
        const a = document.createElement('a');
        a.href = url;
        a.download = `minecraft-js-logs-${this.sessionId}.html`;
        a.style.display = 'none';
        
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        URL.revokeObjectURL(url);
        
        console.log(`📄 Logs sauvegardés: minecraft-js-logs-${this.sessionId}.html (${this.logs.length} entrées)`);
    }
    
    // Générer le HTML des logs
    generateLogHTML() {
        const stats = this.generateStats();
        
        return `<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minecraft JS - Logs de Session ${this.sessionId}</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #e0e0e0;
            margin: 0;
            padding: 20px;
            line-height: 1.4;
        }
        
        .header {
            background: #2d2d2d;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #4CAF50;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: #333;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #4CAF50;
        }
        
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        .filter {
            margin: 10px 0;
        }
        
        .filter select, .filter input {
            background: #333;
            color: #e0e0e0;
            border: 1px solid #555;
            padding: 8px;
            border-radius: 4px;
            margin: 0 10px;
        }
        
        .log-container {
            background: #2d2d2d;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .log-entry {
            border-bottom: 1px solid #444;
            padding: 12px;
            transition: background 0.2s;
        }
        
        .log-entry:hover {
            background: #333;
        }
        
        .log-entry.ERROR {
            border-left: 4px solid #f44336;
            background: rgba(244, 67, 54, 0.1);
        }
        
        .log-entry.WARN {
            border-left: 4px solid #ff9800;
            background: rgba(255, 152, 0, 0.1);
        }
        
        .log-entry.INFO {
            border-left: 4px solid #2196f3;
        }
        
        .log-entry.DEBUG {
            border-left: 4px solid #9c27b0;
        }
        
        .log-entry.MINING {
            border-left: 4px solid #795548;
            background: rgba(121, 85, 72, 0.1);
        }
        
        .log-entry.PHYSICS {
            border-left: 4px solid #607d8b;
        }
        
        .log-entry.CHUNK {
            border-left: 4px solid #4caf50;
        }
        
        .log-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .log-level {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .log-level.ERROR { background: #f44336; color: white; }
        .log-level.WARN { background: #ff9800; color: white; }
        .log-level.INFO { background: #2196f3; color: white; }
        .log-level.DEBUG { background: #9c27b0; color: white; }
        .log-level.MINING { background: #795548; color: white; }
        .log-level.PHYSICS { background: #607d8b; color: white; }
        .log-level.CHUNK { background: #4caf50; color: white; }
        
        .log-time {
            color: #888;
            font-size: 12px;
        }
        
        .log-index {
            color: #666;
            font-size: 12px;
        }
        
        .log-message {
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .log-data {
            background: #1a1a1a;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        
        .hidden {
            display: none;
        }
        
        .search-highlight {
            background: yellow;
            color: black;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎮 Minecraft JS - Logs de Session</h1>
        <p><strong>Session ID:</strong> ${this.sessionId}</p>
        <p><strong>Généré le:</strong> ${new Date().toLocaleString('fr-FR')}</p>
        <p><strong>Total des logs:</strong> ${this.logs.length}</p>
    </div>
    
    <div class="stats">
        <div class="stat-card">
            <div class="stat-number">${stats.ERROR}</div>
            <div>Erreurs</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${stats.WARN}</div>
            <div>Avertissements</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${stats.MINING}</div>
            <div>Logs de Minage</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${stats.PHYSICS}</div>
            <div>Logs de Physique</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${stats.CHUNK}</div>
            <div>Logs de Chunks</div>
        </div>
    </div>
    
    <div class="controls">
        <button class="btn" onclick="exportToText()">📄 Exporter en TXT</button>
        <button class="btn" onclick="exportToJSON()">📋 Exporter en JSON</button>
        <button class="btn" onclick="copyToClipboard()">📋 Copier tout</button>
        <button class="btn" onclick="toggleAllData()">👁️ Toggle Données</button>
    </div>
    
    <div class="filter">
        <label>Filtrer par niveau:</label>
        <select id="levelFilter" onchange="filterLogs()">
            <option value="">Tous</option>
            <option value="ERROR">Erreurs</option>
            <option value="WARN">Avertissements</option>
            <option value="INFO">Info</option>
            <option value="DEBUG">Debug</option>
            <option value="MINING">Minage</option>
            <option value="PHYSICS">Physique</option>
            <option value="CHUNK">Chunks</option>
        </select>
        
        <label>Rechercher:</label>
        <input type="text" id="searchInput" placeholder="Rechercher dans les logs..." oninput="searchLogs()">
        
        <label>Depuis:</label>
        <input type="datetime-local" id="timeFilter" onchange="filterLogs()">
    </div>
    
    <div class="log-container" id="logContainer">
        ${this.logs.map(log => this.generateLogEntryHTML(log)).join('')}
    </div>
    
    <script>
        let showAllData = false;
        
        function filterLogs() {
            const levelFilter = document.getElementById('levelFilter').value;
            const timeFilter = document.getElementById('timeFilter').value;
            const entries = document.querySelectorAll('.log-entry');
            
            entries.forEach(entry => {
                let show = true;
                
                if (levelFilter && !entry.classList.contains(levelFilter)) {
                    show = false;
                }
                
                if (timeFilter) {
                    const entryTime = entry.dataset.timestamp;
                    if (entryTime < timeFilter) {
                        show = false;
                    }
                }
                
                entry.style.display = show ? 'block' : 'none';
            });
        }
        
        function searchLogs() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const entries = document.querySelectorAll('.log-entry');
            
            entries.forEach(entry => {
                const text = entry.textContent.toLowerCase();
                const show = text.includes(searchTerm);
                entry.style.display = show ? 'block' : 'none';
                
                // Highlight search terms
                if (searchTerm && show) {
                    const content = entry.innerHTML;
                    const highlighted = content.replace(
                        new RegExp(searchTerm, 'gi'),
                        '<span class="search-highlight">$&</span>'
                    );
                    entry.innerHTML = highlighted;
                }
            });
        }
        
        function toggleAllData() {
            showAllData = !showAllData;
            const dataElements = document.querySelectorAll('.log-data');
            dataElements.forEach(el => {
                el.style.display = showAllData ? 'block' : 'none';
            });
        }
        
        function exportToText() {
            const logs = ${JSON.stringify(this.logs)};
            const text = logs.map(log => 
                \`[\${log.timestamp}] [\${log.level}] \${log.message}\\n\${JSON.stringify(log.data, null, 2)}\\n---\\n\`
            ).join('');
            
            const blob = new Blob([text], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'minecraft-js-logs-${this.sessionId}.txt';
            a.click();
            URL.revokeObjectURL(url);
        }
        
        function exportToJSON() {
            const logs = ${JSON.stringify(this.logs)};
            const blob = new Blob([JSON.stringify(logs, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'minecraft-js-logs-${this.sessionId}.json';
            a.click();
            URL.revokeObjectURL(url);
        }
        
        function copyToClipboard() {
            const logs = ${JSON.stringify(this.logs)};
            const text = logs.map(log => 
                \`[\${log.index}] [\${log.timestamp}] [\${log.level}] \${log.message}\\n\${JSON.stringify(log.data, null, 2)}\`
            ).join('\\n---\\n');
            
            navigator.clipboard.writeText(text).then(() => {
                alert('Logs copiés dans le presse-papiers !');
            });
        }
        
        // Auto-scroll to bottom
        document.getElementById('logContainer').scrollTop = document.getElementById('logContainer').scrollHeight;
    </script>
</body>
</html>`;
    }
    
    // Générer les statistiques des logs
    generateStats() {
        const stats = {
            ERROR: 0,
            WARN: 0,
            INFO: 0,
            DEBUG: 0,
            MINING: 0,
            PHYSICS: 0,
            CHUNK: 0,
            SYSTEM: 0
        };
        
        this.logs.forEach(log => {
            if (stats.hasOwnProperty(log.level)) {
                stats[log.level]++;
            }
        });
        
        return stats;
    }
    
    // Générer le HTML d'une entrée de log
    generateLogEntryHTML(log) {
        const time = new Date(log.timestamp).toLocaleTimeString('fr-FR');
        const dataStr = JSON.stringify(log.data, null, 2);
        
        return `
        <div class="log-entry ${log.level}" data-timestamp="${log.timestamp}">
            <div class="log-header">
                <div>
                    <span class="log-level ${log.level}">${log.level}</span>
                    <span class="log-index">#${log.index}</span>
                </div>
                <span class="log-time">${time}</span>
            </div>
            <div class="log-message">${log.message}</div>
            <div class="log-data" style="display: none;">${dataStr}</div>
        </div>`;
    }
    
    // Sauvegarder manuellement
    saveNow() {
        console.log('📄 Téléchargement manuel des logs demandé');
        this.saveToFile();
    }
    
    // Obtenir les logs pour analyse externe
    getLogs() {
        return this.logs;
    }
    
    // Nettoyer les logs (garder seulement les plus récents)
    clearOldLogs(keepCount = 100) {
        if (this.logs.length > keepCount) {
            this.logs = this.logs.slice(-keepCount);
            this.log('SYSTEM', `Logs nettoyés - Gardé ${keepCount} entrées les plus récentes`);
        }
    }
}

// Instance globale du logger
window.GameLogger = new Logger();