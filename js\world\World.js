import { Chunk } from './Chunk.js';
import { WorldGenerator } from './WorldGenerator.js';
import { WorkerManager } from '../utils/WorkerManager.js';

export class World {
    constructor(scene, textureGenerator = null) {
        // Récupérer le logger global
        this.logger = window.GameLogger;
        
        // Intégrer le générateur de textures
        this.textureGenerator = textureGenerator;
        
        const WORLD_VERSION = `World-v${Date.now()}`;
        this.logger.info('Système de monde chargé', {
            version: WORLD_VERSION,
            features: {
                cacheBuster: 'Actif',
                rechercheOptimisee: 'Recherche de sol optimisée',
                logsDetailles: 'Activés dans getGroundHeightAt',
                desactivationRechercheSol: 'Implémentée'
            }
        });

        this.version = WORLD_VERSION;
        this.scene = scene;
        this.chunks = new Map();
        this.generator = new WorldGenerator();

        // Configuration des distances de rendu et de charge
        this.renderDistance = 6;
        this.loadDistance = 10;

        // Position du joueur
        this.lastPlayerChunkX = 0;
        this.lastPlayerChunkZ = 0;

        // Gestion des chunks et de leur priorité
        this.generationQueue = [];
        this.maxChunksPerFrame = 2;
        this.workerManager = new WorkerManager();
        this.chunksBeingGenerated = new Set();

        // Cache de visibilité pour réduire les calculs
        this.visibilityCache = new Map();
        this.visibilityCacheTimeout = 500;
        this.lastVisibilityUpdate = 0;

        // Cache pour éviter les appels répétitifs de getGroundHeightAt
        this.groundHeightCache = {};
        this.groundSearchDisabled = false;

        // CORRECTION: Initialisation des hauteurs de sol de secours
        this.cachedGroundHeight = 70; // Hauteur par défaut
        this.lastKnownGoodGroundHeight = 70; // Hauteur de secours pour éviter les chutes

        // Précharger les chunks autour du joueur
        this.preloadChunks(0, 0);

        // Générer immédiatement les chunks centraux
        this.generateInitialChunks();
        this.generateInitialChunks(); // Appel délibérément dupliqué ? Conservé tel quel.

        // Statistiques de performance
        this.stats = {
            chunksGenerated: 0,
            chunksRendered: 0,
            lastFrameTime: performance.now(),
            frameCount: 0
        };
    }

    update(playerX, playerZ) {
        const now = performance.now();
        const delta = now - this.stats.lastFrameTime;
        this.stats.lastFrameTime = now;
        this.stats.frameCount++;

        const chunkX = Math.floor(playerX / 16);
        const chunkZ = Math.floor(playerZ / 16);
        const hasChangedChunk = (chunkX !== this.lastPlayerChunkX || chunkZ !== this.lastPlayerChunkZ);

        if (hasChangedChunk) {
            this.logger.chunk('Joueur déplacé vers nouveau chunk', {
                newChunk: { x: chunkX, z: chunkZ },
                oldChunk: { x: this.lastPlayerChunkX, z: this.lastPlayerChunkZ }
            });
            this.lastPlayerChunkX = chunkX;
            this.lastPlayerChunkZ = chunkZ;
            this.preloadChunks(chunkX, chunkZ);
            this.visibilityCache.clear();
            this.updateVisibility(playerX, playerZ);
            this.lastVisibilityUpdate = now;
        } else if (now - this.lastVisibilityUpdate > this.visibilityCacheTimeout) {
            this.updateVisibility(playerX, playerZ);
            this.lastVisibilityUpdate = now;
        }

        const missingVisibleChunks = this.checkForMissingVisibleChunks(chunkX, chunkZ);
        if (missingVisibleChunks > 0) {
            this.maxChunksPerFrame = Math.min(this.maxChunksPerFrame + 1, 5);
        } else {
            this.maxChunksPerFrame = 2;
        }

        this.generateChunksInQueue();

        if (this.stats.frameCount % 300 === 0) {
            this.logger.debug('Statistiques de performance', {
                fps: Math.round(1000 / delta),
                chunksGenerated: this.stats.chunksGenerated,
                chunksRendered: this.stats.chunksRendered,
                chunksInQueue: this.generationQueue.length,
                chunksBeingGenerated: this.chunksBeingGenerated.size
            });
        }
    }

    preloadChunks(chunkX, chunkZ) {
        const chunksToLoad = [];
        for (let x = -this.loadDistance; x <= this.loadDistance; x++) {
            for (let z = -this.loadDistance; z <= this.loadDistance; z++) {
                const targetX = chunkX + x;
                const targetZ = chunkZ + z;
                const key = `${targetX},${targetZ}`;
                if (!this.chunks.has(key)) {
                    const distance = Math.sqrt(x * x + z * z);
                    chunksToLoad.push({
                        x: targetX,
                        z: targetZ,
                        key,
                        distance,
                        priority: distance <= this.renderDistance ? 1 : 2
                    });
                }
            }
        }

        chunksToLoad.sort((a, b) => {
            if (a.priority !== b.priority) return a.priority - b.priority;
            return a.distance - b.distance;
        });

        const existingKeys = new Set(this.generationQueue.map(chunk => chunk.key));
        const newChunks = chunksToLoad.filter(chunk => !existingKeys.has(chunk.key));

        const maxQueueSize = 100;
        if (this.generationQueue.length + newChunks.length > maxQueueSize) {
            const spaceAvailable = Math.max(0, maxQueueSize - this.generationQueue.length);
            newChunks.splice(spaceAvailable);
        }

        this.generationQueue = this.generationQueue.concat(newChunks);
    }

    async generateInitialChunks() {
        this.logger.info('Génération des chunks initiaux...');
        const centerChunks = [];
        const radius = 2;
        for (let x = -radius; x <= radius; x++) {
            for (let z = -radius; z <= radius; z++) {
                const distance = Math.sqrt(x * x + z * z);
                centerChunks.push({ x, z, priority: distance });
            }
        }
        centerChunks.sort((a, b) => a.priority - b.priority);

        const batchSize = 5;
        this.logger.chunk('Préparation des chunks initiaux', { totalChunks: centerChunks.length, radius, batchSize });
        let processedCount = 0;

        while (processedCount < centerChunks.length) {
            const batch = centerChunks.slice(processedCount, processedCount + batchSize);
            processedCount += batchSize;

            const promises = batch.map(async (chunkData) => {
                const key = `${chunkData.x},${chunkData.z}`;
                if (!this.chunks.has(key)) {
                    try {
                        const blocks = await this.workerManager.generateChunk(chunkData.x, chunkData.z);
                        const chunk = new Chunk(chunkData.x, chunkData.z, null);
                        chunk.blocks = blocks;
                        const mesh = chunk.buildMesh();
                        this.scene.add(mesh);
                        this.chunks.set(key, { chunk, mesh, visible: true });
                        this.stats.chunksGenerated++;
                        this.stats.chunksRendered++;
                        this.logger.chunk('Chunk généré avec succès', { chunkKey: key, position: { x: chunkData.x, z: chunkData.z }, blocksCount: blocks ? blocks.length : 0 });
                        return key;
                    } catch (error) {
                        this.logger.error('Erreur lors de la génération du chunk', { chunkKey: key, position: { x: chunkData.x, z: chunkData.z }, error: error.message, stack: error.stack });
                        return null;
                    }
                }
                return key;
            });

            const results = await Promise.allSettled(promises);
            const successCount = results.filter(result => result.status === 'fulfilled' && result.value).length;
            this.logger.chunk('Lot de chunks généré', { successCount, totalInBatch: batch.length, processedTotal: processedCount });
            await new Promise(resolve => setTimeout(resolve, 10));
        }

        this.logger.info('Génération de chunks initiaux terminée', { totalGenerated: this.stats.chunksGenerated, totalRendered: this.stats.chunksRendered });
    }

    generateChunksInQueue() {
        const toGenerate = Math.min(this.generationQueue.length, this.maxChunksPerFrame);
        if (toGenerate <= 0) return;

        for (let i = 0; i < toGenerate; i++) {
            const chunkData = this.generationQueue.shift();
            if (!chunkData) continue;
            const chunkKey = `${chunkData.x},${chunkData.z}`;
            if (this.chunks.has(chunkKey) || this.chunksBeingGenerated.has(chunkKey)) continue;

            this.logger.chunk('Début génération chunk asynchrone', { position: { x: chunkData.x, z: chunkData.z }, priority: chunkData.priority, queueRemaining: this.generationQueue.length });
            this.chunksBeingGenerated.add(chunkKey);

            this.workerManager.generateChunk(chunkData.x, chunkData.z)
                .then(blocks => {
                    this.chunksBeingGenerated.delete(chunkKey);
                    if (this.chunks.has(chunkKey)) return;

                    this.logger.chunk('Données reçues du worker', { position: { x: chunkData.x, z: chunkData.z }, blocksReceived: blocks ? blocks.length : 0 });
                    const chunk = new Chunk(chunkData.x, chunkData.z, null);
                    chunk.blocks = blocks;

                    // CORRECTION: Toujours créer le mesh pour les chunks proches, même s'ils semblent hors distance
                    const currentDistance = Math.sqrt(
                        Math.pow(chunkData.x - this.lastPlayerChunkX, 2) +
                        Math.pow(chunkData.z - this.lastPlayerChunkZ, 2)
                    );

                    if (currentDistance <= this.renderDistance + 1) { // +1 pour la marge
                        const mesh = chunk.buildMesh();
                        this.scene.add(mesh);
                        this.chunks.set(chunkKey, { chunk, mesh, visible: true });
                        this.stats.chunksRendered++;
                        this.logger.chunk('Chunk ajouté à la scène', {
                            position: { x: chunkData.x, z: chunkData.z },
                            distance: currentDistance.toFixed(2),
                            totalRendered: this.stats.chunksRendered,
                            visible: true
                        });
                    } else {
                        this.chunks.set(chunkKey, { chunk, mesh: null, visible: false });
                        this.logger.chunk('Chunk stocké sans mesh', {
                            position: { x: chunkData.x, z: chunkData.z },
                            distance: currentDistance.toFixed(2),
                            reason: 'Hors distance de rendu'
                        });
                    }
                    this.stats.chunksGenerated++;
                })
                .catch(error => {
                    this.chunksBeingGenerated.delete(chunkKey);
                    this.logger.error('Erreur génération chunk asynchrone', { position: { x: chunkData.x, z: chunkData.z }, error: error.message, stack: error.stack });
                });
        }
    }

    updateVisibility(playerX, playerZ) {
        const playerChunkX = Math.floor(playerX / 16);
        const playerChunkZ = Math.floor(playerZ / 16);
        let meshesCreated = 0;

        for (const [key, chunkData] of this.chunks.entries()) {
            const [x, z] = key.split(',').map(Number);
            const distance = Math.sqrt(Math.pow(x - playerChunkX, 2) + Math.pow(z - playerChunkZ, 2));
            const shouldBeVisible = distance <= this.renderDistance;

            if (shouldBeVisible !== chunkData.visible) {
                if (shouldBeVisible) {
                    if (!chunkData.mesh) {
                        const mesh = chunkData.chunk.buildMesh();
                        this.scene.add(mesh);
                        chunkData.mesh = mesh;
                        this.stats.chunksRendered++;
                        meshesCreated++;
                        this.logger.chunk('Mesh créé pour chunk visible', {
                            position: { x, z },
                            distance: distance.toFixed(2)
                        });
                    } else {
                        chunkData.mesh.visible = true;
                    }
                } else {
                    if (chunkData.mesh) {
                        chunkData.mesh.visible = false;
                    }
                }
                chunkData.visible = shouldBeVisible;
            }
        }

        if (meshesCreated > 0) {
            this.logger.chunk(`${meshesCreated} nouveaux meshes créés lors de la mise à jour de visibilité`);
        }
    }

    // Nouvelle méthode pour forcer la génération de meshes visibles
    generateVisibleChunks() {
        const playerChunkX = this.lastPlayerChunkX;
        const playerChunkZ = this.lastPlayerChunkZ;
        let meshesCreated = 0;

        this.logger.chunk('Forçage de génération de meshes visibles', {
            playerChunk: { x: playerChunkX, z: playerChunkZ },
            renderDistance: this.renderDistance
        });

        for (const [key, chunkData] of this.chunks.entries()) {
            const [x, z] = key.split(',').map(Number);
            const distance = Math.sqrt(Math.pow(x - playerChunkX, 2) + Math.pow(z - playerChunkZ, 2));

            if (distance <= this.renderDistance && !chunkData.mesh) {
                try {
                    const mesh = chunkData.chunk.buildMesh();
                    this.scene.add(mesh);
                    chunkData.mesh = mesh;
                    chunkData.visible = true;
                    this.stats.chunksRendered++;
                    meshesCreated++;
                    this.logger.chunk('Mesh forcé créé', {
                        position: { x, z },
                        distance: distance.toFixed(2)
                    });
                } catch (error) {
                    this.logger.error('Erreur lors de la création forcée de mesh', {
                        position: { x, z },
                        error: error.message
                    });
                }
            }
        }

        this.logger.chunk(`Génération forcée terminée: ${meshesCreated} meshes créés`);
        return meshesCreated;
    }

    isChunkInRenderDistance(x, z) {
        const distance = Math.sqrt(Math.pow(x - this.lastPlayerChunkX, 2) + Math.pow(z - this.lastPlayerChunkZ, 2));
        return distance <= this.renderDistance;
    }

    checkForMissingVisibleChunks(playerChunkX, playerChunkZ) {
        let missingCount = 0;
        let newChunksAdded = 0;
        for (let x = playerChunkX - this.renderDistance; x <= playerChunkX + this.renderDistance; x++) {
            for (let z = playerChunkZ - this.renderDistance; z <= playerChunkZ + this.renderDistance; z++) {
                const distance = Math.sqrt(Math.pow(x - playerChunkX, 2) + Math.pow(z - playerChunkZ, 2));
                if (distance <= this.renderDistance) {
                    const key = `${x},${z}`;
                    if (!this.chunks.has(key)) {
                        const isInQueue = this.generationQueue.some(item => item.x === x && item.z === z);
                        const isBeingGenerated = this.chunksBeingGenerated.has(key);
                        if (!isInQueue && !isBeingGenerated) {
                            missingCount++;
                            newChunksAdded++;
                            this.generationQueue.push({ x, z, key, priority: distance * 0.5 });
                            // MODIFIÉ: Remplacement de console.log par le logger
                            this.logger.chunk(`Chunk manquant détecté: (${x}, ${z}), distance: ${distance.toFixed(2)}`);
                        }
                    }
                }
            }
        }

        if (newChunksAdded > 0) {
            this.generationQueue.sort((a, b) => a.priority - b.priority);
            // MODIFIÉ: Remplacement de console.log par le logger
            this.logger.chunk(`${newChunksAdded} chunks manquants détectés et ajoutés à la file`);
        }
        return missingCount;
    }

    hasCollisionAt(x, y, z, radius = 0.3) {
        const chunkX = Math.floor(x / 16);
        const chunkZ = Math.floor(z / 16);
        const key = `${chunkX},${chunkZ}`;
        if (!this.chunks.has(key)) return false;
        const chunkData = this.chunks.get(key);
        const chunk = chunkData.chunk;
        if (!chunk || !chunk.blocks) return false;

        let localX = Math.floor(x) - chunkX * 16;
        let localZ = Math.floor(z) - chunkZ * 16;
        const localY = Math.floor(y);
        localX = Math.max(0, Math.min(15, localX));
        localZ = Math.max(0, Math.min(15, localZ));

        for (let offsetY = 0; offsetY <= 1; offsetY++) {
            const checkY = localY + offsetY;
            if (checkY < 0 || checkY >= 128) continue;
            for (let offsetX = -1; offsetX <= 1; offsetX++) {
                for (let offsetZ = -1; offsetZ <= 1; offsetZ++) {
                    const checkX = localX + offsetX;
                    const checkZ = localZ + offsetZ;
                    if (checkX < 0 || checkX >= 16 || checkZ < 0 || checkZ >= 16) continue;

                    const blockType = chunk.getBlockAt(checkX, checkY, checkZ);
                    if (blockType !== 0) {
                        const blockWorldX = chunkX * 16 + checkX;
                        const blockWorldZ = chunkZ * 16 + checkZ;
                        const dx = Math.abs(x - (blockWorldX + 0.5));
                        const dy = Math.abs(y - (checkY + 0.5));
                        const dz = Math.abs(z - (blockWorldZ + 0.5));
                        if (dx < radius + 0.5 && dy < radius + 0.5 && dz < radius + 0.5) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }
    
    // ===== CORRECTION PRINCIPALE APPLIQUÉE ICI =====
    getGroundHeightAt(x, z) {
        const cacheKey = `${Math.floor(x)},${Math.floor(z)}`;
        const now = Date.now();
        
        if (this.groundHeightCache && this.groundHeightCache[cacheKey]) {
            const cached = this.groundHeightCache[cacheKey];
            if (now - cached.time < 1000) {
                return cached.height;
            }
        }

        const chunkX = Math.floor(x / 16);
        const chunkZ = Math.floor(z / 16);
        const key = `${chunkX},${chunkZ}`;

        // MODIFIÉ: Si le chunk n'est pas prêt, retourner la dernière hauteur connue
        // au lieu de null pour empêcher le joueur de tomber.
        if (!this.chunks.has(key)) {
            return this.lastKnownGoodGroundHeight;
        }

        const chunkData = this.chunks.get(key);
        const chunk = chunkData.chunk;

        // MODIFIÉ: Sécurité supplémentaire si le chunk est malformé.
        if (!chunk || !chunk.blocks || typeof chunk.getBlockAt !== 'function') {
            return this.lastKnownGoodGroundHeight;
        }

        let localX = Math.floor(x) - chunkX * 16;
        let localZ = Math.floor(z) - chunkZ * 16;
        localX = Math.max(0, Math.min(15, localX));
        localZ = Math.max(0, Math.min(15, localZ));

        for (let y = 127; y >= 0; y--) {
            try {
                const blockType = chunk.getBlockAt(localX, y, localZ);
                if (blockType && blockType !== 0) {
                    if (!this.groundHeightCache) this.groundHeightCache = {};
                    this.groundHeightCache[cacheKey] = { height: y, time: now };
                    
                    // MODIFIÉ: Mettre à jour la dernière hauteur de sol valide
                    this.lastKnownGoodGroundHeight = y;
                    
                    return y;
                }
            } catch (error) {
                continue;
            }
        }

        // MODIFIÉ: Si aucun sol n'est trouvé, retourner la dernière hauteur connue
        // comme solution de repli ultime.
        return this.lastKnownGoodGroundHeight;
    }

    disableGroundSearch() {
        this.groundSearchDisabled = true;
        // MODIFIÉ: Remplacement de console.log par le logger
        this.logger.warn(`Recherche de sol désactivée définitivement dans World.js`);
    }

    isClimbableBlock(blockType) {
        const CLIMBABLE_BLOCKS = [ 1, 2, 3, 4, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17 ];
        return CLIMBABLE_BLOCKS.includes(blockType);
    }

    getBlockTypeAt(x, y, z) {
        const chunkX = Math.floor(x / 16);
        const chunkZ = Math.floor(z / 16);
        const key = `${chunkX},${chunkZ}`;
        if (!this.chunks.has(key)) return 0;
        const chunkData = this.chunks.get(key);
        const chunk = chunkData.chunk;
        if (!chunk || !chunk.blocks) return 0;
        let localX = Math.floor(x) - chunkX * 16;
        let localZ = Math.floor(z) - chunkZ * 16;
        if (localX < 0) localX = 0; if (localX >= 16) localX = 15;
        if (localZ < 0) localZ = 0; if (localZ >= 16) localZ = 15;
        if (y < 0 || y >= 128) return 0;
        return chunk.getBlockAt(localX, y, localZ);
    }

    setBlockAt(x, y, z, blockType) {
        const chunkX = Math.floor(x / 16);
        const chunkZ = Math.floor(z / 16);
        const key = `${chunkX},${chunkZ}`;
        if (!this.chunks.has(key)) {
            this.logger.warn(`Chunk ${key} n'existe pas pour setBlockAt`);
            return false;
        }
        const chunkData = this.chunks.get(key);
        const chunk = chunkData.chunk;
        if (!chunk || !chunk.blocks) {
            this.logger.warn(`Chunk ${key} n'a pas de blocs pour setBlockAt`);
            return false;
        }
        let localX = Math.floor(x) - chunkX * 16;
        let localZ = Math.floor(z) - chunkZ * 16;
        if (localX < 0) localX = 0; if (localX >= 16) localX = 15;
        if (localZ < 0) localZ = 0; if (localZ >= 16) localZ = 15;
        if (y < 0 || y >= 128) return false;

        const success = chunk.setBlockAt(localX, y, localZ, blockType);
        if (!success) {
            this.logger.error(`Échec de la modification du bloc à (${localX}, ${y}, ${localZ})`);
            return false;
        }

        if (typeof chunk.generateMesh === 'function') {
            chunk.generateMesh();
        } else {
            this.logger.warn(`Méthode generateMesh non disponible, reconstruction manuelle...`, { chunkKey: key });
            if (chunkData.mesh && chunkData.mesh.parent) {
                chunkData.mesh.parent.remove(chunkData.mesh);
                if (chunkData.mesh.isGroup) {
                    while (chunkData.mesh.children.length > 0) { /* ... nettoyage ... */ }
                } else { /* ... nettoyage ... */ }
            }
            const newMesh = chunk.buildMesh();
            if (newMesh) {
                this.scene.add(newMesh);
                chunkData.mesh = newMesh;
                this.logger.info(`Mesh du chunk (${chunkX}, ${chunkZ}) reconstruit`, { chunkKey: key });
            } else {
                this.logger.error(`Échec de la reconstruction du mesh`, { chunkKey: key });
            }
        }
        this.logger.info(`Bloc modifié à (${x}, ${y}, ${z}): ${blockType === 0 ? 'détruit' : 'placé'}`);
        return true;
    }

    cleanGroundHeightCache() {
        const now = Date.now();
        if (!this.lastCacheCleanup || now - this.lastCacheCleanup > 30000) {
            this.groundHeightCache = {};
            this.lastCacheCleanup = now;
            this.logger.debug('[WORLD] Cache de hauteur du sol nettoyé');
        }
    }

    disableGroundSearchLogs() {
        this.groundSearchDisabled = true;
        setTimeout(() => { this.groundSearchDisabled = false; }, 5000);
    }
}