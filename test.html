<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Test Minecraft JS</title>
    <style>
        body { margin: 0; background: #000; color: white; font-family: Arial, sans-serif; }
        #info { position: absolute; top: 10px; left: 10px; z-index: 100; }
    </style>
</head>
<body>
    <div id="info">Test de chargement...</div>
    <canvas id="game"></canvas>

    <script src="https://unpkg.com/three@0.155.0/build/three.min.js"></script>
    <script src="js/utils/SimplexNoise.js"></script>
    <script>
        const info = document.getElementById('info');
        
        // Test 1: Three.js
        if (window.THREE) {
            info.innerHTML += '<br>✓ Three.js chargé (version ' + THREE.REVISION + ')';
        } else {
            info.innerHTML += '<br>✗ Three.js non chargé';
        }
        
        // Test 2: SimplexNoise
        if (window.SimplexNoise) {
            info.innerHTML += '<br>✓ SimplexNoise chargé';
            
            // Test de base
            const noise = new SimplexNoise(42);
            const testValue = noise.noise2D(0, 0);
            info.innerHTML += '<br>✓ Test noise: ' + testValue.toFixed(3);
        } else {
            info.innerHTML += '<br>✗ SimplexNoise non chargé';
        }
        
        // Test 3: Canvas et WebGL
        const canvas = document.getElementById('game');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        if (gl) {
            info.innerHTML += '<br>✓ WebGL supporté';
        } else {
            info.innerHTML += '<br>✗ WebGL non supporté';
        }
        
        // Test 4: Création d'une scène simple
        try {
            const scene = new THREE.Scene();
            const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            const renderer = new THREE.WebGLRenderer({ canvas: canvas });
            
            renderer.setSize(window.innerWidth, window.innerHeight);
            scene.background = new THREE.Color(0x87CEEB);
            
            // Créer un cube simple
            const geometry = new THREE.BoxGeometry();
            const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
            const cube = new THREE.Mesh(geometry, material);
            scene.add(cube);
            
            camera.position.z = 5;
            
            function animate() {
                requestAnimationFrame(animate);
                cube.rotation.x += 0.01;
                cube.rotation.y += 0.01;
                renderer.render(scene, camera);
            }
            
            animate();
            info.innerHTML += '<br>✓ Scène Three.js créée avec succès';
            
        } catch (error) {
            info.innerHTML += '<br>✗ Erreur création scène: ' + error.message;
        }
    </script>
</body>
</html>