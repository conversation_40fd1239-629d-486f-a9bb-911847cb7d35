// DIAGNOSTIC DES LOGS - Analyse en temps réel des problèmes de saut
console.log('🔍 DIAGNOSTIC DES LOGS - Démarrage...');

class LogDiagnostic {
    constructor() {
        this.jumpCount = 0;
        this.lastJumpTime = 0;
        this.jumpSpamDetected = false;
        this.performanceIssues = [];
        this.startTime = Date.now();
        
        // Intercepter les logs de physique
        this.interceptPhysicsLogs();
        
        // Surveiller les performances
        this.monitorPerformance();
        
        console.log('✅ Diagnostic initialisé');
    }
    
    interceptPhysicsLogs() {
        // Intercepter les logs du GameLogger si disponible
        if (window.GameLogger && window.GameLogger.physics) {
            const originalPhysics = window.GameLogger.physics;
            
            window.GameLogger.physics = (message, data) => {
                // Appeler la fonction originale
                originalPhysics.call(window.GameLogger, message, data);
                
                // Analyser les logs de saut
                if (message === 'Saut effectué') {
                    this.analyzeSautLog(data);
                }
            };
            
            console.log('🔍 Interception des logs de physique activée');
        }
    }
    
    analyzeSautLog(data) {
        const currentTime = Date.now();
        this.jumpCount++;
        
        // Détecter le spam de saut (plus de 3 sauts en moins d'une seconde)
        if (currentTime - this.lastJumpTime < 1000) {
            if (this.jumpCount > 3) {
                this.jumpSpamDetected = true;
                console.error('🚨 SPAM DE SAUT DÉTECTÉ!', {
                    jumpCount: this.jumpCount,
                    timeWindow: currentTime - this.lastJumpTime,
                    data: data
                });
            }
        } else {
            // Réinitialiser le compteur après 1 seconde
            this.jumpCount = 1;
            this.jumpSpamDetected = false;
        }
        
        this.lastJumpTime = currentTime;
        
        // Log détaillé du saut
        console.log('🦘 Saut analysé:', {
            jumpNumber: this.jumpCount,
            timeSinceLastJump: currentTime - this.lastJumpTime,
            spamDetected: this.jumpSpamDetected,
            data: data
        });
    }
    
    monitorPerformance() {
        let frameCount = 0;
        let lastTime = performance.now();
        
        const checkPerformance = () => {
            const currentTime = performance.now();
            frameCount++;
            
            if (currentTime - lastTime >= 1000) {
                const fps = Math.round(frameCount * 1000 / (currentTime - lastTime));
                
                if (fps < 50) {
                    this.performanceIssues.push({
                        time: new Date().toLocaleTimeString(),
                        fps: fps,
                        jumpSpamActive: this.jumpSpamDetected
                    });
                    
                    console.warn('⚠️ PERFORMANCE DÉGRADÉE:', {
                        fps: fps,
                        jumpSpamActive: this.jumpSpamDetected,
                        totalIssues: this.performanceIssues.length
                    });
                }
                
                frameCount = 0;
                lastTime = currentTime;
            }
            
            requestAnimationFrame(checkPerformance);
        };
        
        requestAnimationFrame(checkPerformance);
        console.log('📊 Surveillance des performances activée');
    }
    
    generateReport() {
        const uptime = Date.now() - this.startTime;
        
        return {
            uptime: Math.round(uptime / 1000),
            totalJumps: this.jumpCount,
            jumpSpamDetected: this.jumpSpamDetected,
            performanceIssues: this.performanceIssues.length,
            averageFPS: this.performanceIssues.length > 0 ? 
                Math.round(this.performanceIssues.reduce((sum, issue) => sum + issue.fps, 0) / this.performanceIssues.length) : 
                'Aucun problème détecté',
            recommendations: this.getRecommendations()
        };
    }
    
    getRecommendations() {
        const recommendations = [];
        
        if (this.jumpSpamDetected) {
            recommendations.push('Vérifier la logique de cooldown du saut');
            recommendations.push('Vérifier que jumpPressed est correctement géré');
        }
        
        if (this.performanceIssues.length > 0) {
            recommendations.push('Optimiser la boucle de rendu');
            recommendations.push('Vérifier les fuites mémoire');
        }
        
        if (recommendations.length === 0) {
            recommendations.push('Aucun problème détecté - corrections efficaces');
        }
        
        return recommendations;
    }
}

// Initialiser le diagnostic automatiquement
let diagnostic;

function initDiagnostic() {
    if (window.GameLogger) {
        diagnostic = new LogDiagnostic();
        
        // Ajouter une fonction globale pour générer un rapport
        window.generateDiagnosticReport = () => {
            const report = diagnostic.generateReport();
            console.log('📋 RAPPORT DE DIAGNOSTIC:', report);
            return report;
        };
        
        console.log('🔍 Diagnostic prêt. Utilisez generateDiagnosticReport() pour un rapport.');
    } else {
        console.log('⏳ GameLogger non disponible, nouvelle tentative dans 1s...');
        setTimeout(initDiagnostic, 1000);
    }
}

// Démarrer le diagnostic
initDiagnostic();

// Export pour utilisation dans d'autres scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LogDiagnostic;
}
