// Gestionnaire des options et paramètres du jeu
export class OptionsManager {
    constructor() {
        this.logger = window.GameLogger;
        this.isOpen = false;
        
        // Version des paramètres pour la compatibilité
        this.settingsVersion = "1.0.0";
        
        // Paramètres par défaut
        this.defaultSettings = {
            // Version et compatibilité
            version: this.settingsVersion,
            
            // Rendu et couleurs
            contrast: 100,
            brightness: 100,
            saturation: 100,
            gamma: 100,
            hdrMode: false,
            colorCorrection: false,
            
            // Modes de couleur Minecraft
            colorMode: 'default', // default, vibrant, moody, classic, warm, cool
            
            // Gameplay
            mouseSensitivity: 2.0,
            fov: 75,
            
            // Performance
            renderDistance: 6, // Maintenant de 2 à 32 chunks
            vsync: false,
            
            // Raccourcis personnalisés
            keyBindings: {
                emergencySpawn: 'F9',
                resetPhysics: 'F10',
                downloadLogs: 'F11'
            },
            
            // Options de gameplay avancées
            autoClimb: true, // Escalade automatique des blocs de terrain
            autoClimbTrees: false // Escalade automatique des arbres (séparée)
        };
        
        // Charger les paramètres sauvegardés ou utiliser les défauts
        this.settings = this.loadSettings();
        
        // Initialiser l'interface
        this.initializeUI();
        
        // Appliquer les paramètres au démarrage
        this.applyAllSettings();
        
        this.logger.info('OptionsManager initialisé', {
            settings: this.settings
        });
    }
    
    // Charger les paramètres depuis localStorage avec gestion de version
    loadSettings() {
        try {
            const saved = localStorage.getItem('minecraft-js-settings-v2');
            if (saved) {
                const parsed = JSON.parse(saved);
                
                // Vérifier la compatibilité de version
                if (parsed.version && this.isVersionCompatible(parsed.version)) {
                    return { ...this.defaultSettings, ...parsed };
                } else {
                    // Migration depuis ancienne version
                    this.logger.info('Migration des paramètres depuis ancienne version');
                    const migrated = this.migrateSettings(parsed);
                    this.saveSettings(migrated);
                    return migrated;
                }
            }
            
            // Essayer de charger depuis l'ancien système
            const oldSaved = localStorage.getItem('minecraft-js-settings');
            if (oldSaved) {
                const oldParsed = JSON.parse(oldSaved);
                const migrated = this.migrateSettings(oldParsed);
                this.saveSettings(migrated);
                localStorage.removeItem('minecraft-js-settings'); // Nettoyer l'ancien
                return migrated;
            }
        } catch (error) {
            this.logger.warn('Erreur lors du chargement des paramètres', {
                error: error.message
            });
        }
        return { ...this.defaultSettings };
    }
    
    // Vérifier la compatibilité de version
    isVersionCompatible(version) {
        const current = this.settingsVersion.split('.').map(Number);
        const saved = version.split('.').map(Number);
        
        // Compatible si version majeure identique
        return current[0] === saved[0];
    }
    
    // Migrer les paramètres depuis une ancienne version
    migrateSettings(oldSettings) {
        const migrated = { ...this.defaultSettings };
        
        // Copier les paramètres compatibles
        Object.keys(oldSettings).forEach(key => {
            if (key in this.defaultSettings && key !== 'version') {
                migrated[key] = oldSettings[key];
            }
        });
        
        migrated.version = this.settingsVersion;
        this.logger.info('Paramètres migrés avec succès');
        return migrated;
    }
    
    // Sauvegarder les paramètres dans localStorage avec versioning
    saveSettings(settingsToSave = null) {
        try {
            const settings = settingsToSave || this.settings;
            settings.version = this.settingsVersion;
            
            localStorage.setItem('minecraft-js-settings-v2', JSON.stringify(settings));
            
            // Sauvegarde de secours dans une clé différente
            localStorage.setItem('minecraft-js-settings-backup', JSON.stringify(settings));
            
            this.logger.info('Paramètres sauvegardés avec versioning', {
                version: settings.version,
                settingsCount: Object.keys(settings).length
            });
        } catch (error) {
            this.logger.error('Erreur lors de la sauvegarde des paramètres', {
                error: error.message
            });
        }
    }
    
    // Initialiser l'interface utilisateur
    initializeUI() {
        // Gestionnaire pour ouvrir/fermer le menu avec F1
        document.addEventListener('keydown', (event) => {
            if (event.key === 'F1') {
                event.preventDefault();
                this.toggle();
            }
            
            // Fermer avec Échap
            if (event.key === 'Escape' && this.isOpen) {
                event.preventDefault();
                this.close();
            }
        });
        
        // Bouton fermer
        const closeButton = document.getElementById('close-options');
        if (closeButton) {
            closeButton.addEventListener('click', () => this.close());
        }
        
        // Bouton réinitialiser
        const resetButton = document.getElementById('reset-options');
        if (resetButton) {
            resetButton.addEventListener('click', () => this.resetToDefaults());
        }
        
        // Initialiser tous les contrôles
        this.initializeControls();
        
        // Mettre à jour l'affichage des valeurs
        this.updateDisplayValues();
    }
    
    // Initialiser les contrôles individuels
    initializeControls() {
        // Mode de couleur
        const colorModeSelect = document.getElementById('color-mode');
        if (colorModeSelect) {
            colorModeSelect.value = this.settings.colorMode;
            colorModeSelect.addEventListener('change', (e) => {
                this.settings.colorMode = e.target.value;
                this.applyColorMode();
                this.saveSettings();
                this.logger.info('Mode de couleur changé', { mode: this.settings.colorMode });
            });
        }
        
        // Contraste
        const contrastSlider = document.getElementById('contrast-slider');
        if (contrastSlider) {
            contrastSlider.value = this.settings.contrast;
            contrastSlider.addEventListener('input', (e) => {
                this.settings.contrast = parseInt(e.target.value);
                this.updateDisplayValue('contrast-value', this.settings.contrast + '%');
                this.applyColorSettings();
                this.saveSettings();
            });
        }
        
        // Luminosité
        const brightnessSlider = document.getElementById('brightness-slider');
        if (brightnessSlider) {
            brightnessSlider.value = this.settings.brightness;
            brightnessSlider.addEventListener('input', (e) => {
                this.settings.brightness = parseInt(e.target.value);
                this.updateDisplayValue('brightness-value', this.settings.brightness + '%');
                this.applyColorSettings();
                this.saveSettings();
            });
        }
        
        // Saturation
        const saturationSlider = document.getElementById('saturation-slider');
        if (saturationSlider) {
            saturationSlider.value = this.settings.saturation;
            saturationSlider.addEventListener('input', (e) => {
                this.settings.saturation = parseInt(e.target.value);
                this.updateDisplayValue('saturation-value', this.settings.saturation + '%');
                this.applyColorSettings();
                this.saveSettings();
            });
        }
        
        // Gamma
        const gammaSlider = document.getElementById('gamma-slider');
        if (gammaSlider) {
            gammaSlider.value = this.settings.gamma;
            gammaSlider.addEventListener('input', (e) => {
                this.settings.gamma = parseInt(e.target.value);
                const gammaValue = (this.settings.gamma / 100).toFixed(1);
                this.updateDisplayValue('gamma-value', gammaValue);
                this.applyColorSettings();
                this.saveSettings();
            });
        }
        
        // Mode HDR
        const hdrCheckbox = document.getElementById('hdr-mode');
        if (hdrCheckbox) {
            hdrCheckbox.checked = this.settings.hdrMode;
            hdrCheckbox.addEventListener('change', (e) => {
                this.settings.hdrMode = e.target.checked;
                this.applyColorSettings();
                this.saveSettings();
                this.logger.info('Mode HDR changé', { enabled: this.settings.hdrMode });
            });
        }
        
        // Correction des couleurs
        const colorCorrectionCheckbox = document.getElementById('color-correction');
        if (colorCorrectionCheckbox) {
            colorCorrectionCheckbox.checked = this.settings.colorCorrection;
            colorCorrectionCheckbox.addEventListener('change', (e) => {
                this.settings.colorCorrection = e.target.checked;
                this.applyColorSettings();
                this.saveSettings();
                this.logger.info('Correction des couleurs changée', { enabled: this.settings.colorCorrection });
            });
        }
        
        // Sensibilité souris
        const sensitivitySlider = document.getElementById('mouse-sensitivity');
        if (sensitivitySlider) {
            sensitivitySlider.value = this.settings.mouseSensitivity;
            sensitivitySlider.addEventListener('input', (e) => {
                this.settings.mouseSensitivity = parseFloat(e.target.value);
                this.updateDisplayValue('sensitivity-value', this.settings.mouseSensitivity.toFixed(1));
                this.applyMouseSensitivity();
                this.saveSettings();
            });
        }
        
        // FOV
        const fovSlider = document.getElementById('fov-slider');
        if (fovSlider) {
            fovSlider.value = this.settings.fov;
            fovSlider.addEventListener('input', (e) => {
                this.settings.fov = parseInt(e.target.value);
                this.updateDisplayValue('fov-value', this.settings.fov + '°');
                this.applyFOV();
                this.saveSettings();
            });
        }
        
        // Distance de rendu (maintenant de 2 à 32 chunks)
        const renderDistanceSlider = document.getElementById('render-distance');
        if (renderDistanceSlider) {
            renderDistanceSlider.value = this.settings.renderDistance;
            renderDistanceSlider.addEventListener('input', (e) => {
                this.settings.renderDistance = parseInt(e.target.value);
                this.updateDisplayValue('render-distance-value', this.settings.renderDistance + ' chunks');
                this.applyRenderDistance();
                this.saveSettings();
                
                // Avertissement pour les distances élevées
                if (this.settings.renderDistance > 16) {
                    if (window.showNotification) {
                        window.showNotification('⚠️ Distance élevée - Impact sur les performances', 'warning');
                    }
                }
            });
        }
        
        // V-Sync
        const vsyncCheckbox = document.getElementById('vsync-mode');
        if (vsyncCheckbox) {
            vsyncCheckbox.checked = this.settings.vsync;
            vsyncCheckbox.addEventListener('change', (e) => {
                this.settings.vsync = e.target.checked;
                this.saveSettings();
                this.logger.info('V-Sync changé', { enabled: this.settings.vsync });
            });
        }
        
        // Escalade automatique (terrain)
        const autoClimbCheckbox = document.getElementById('auto-climb');
        if (autoClimbCheckbox) {
            autoClimbCheckbox.checked = this.settings.autoClimb;
            autoClimbCheckbox.addEventListener('change', (e) => {
                this.settings.autoClimb = e.target.checked;
                this.applyClimbSettings();
                this.saveSettings();
                this.logger.info('Escalade automatique (terrain) changée', { enabled: this.settings.autoClimb });
            });
        }
        
        // Escalade automatique (arbres)
        const autoClimbTreesCheckbox = document.getElementById('auto-climb-trees');
        if (autoClimbTreesCheckbox) {
            autoClimbTreesCheckbox.checked = this.settings.autoClimbTrees;
            autoClimbTreesCheckbox.addEventListener('change', (e) => {
                this.settings.autoClimbTrees = e.target.checked;
                this.applyClimbSettings();
                this.saveSettings();
                this.logger.info('Escalade automatique (arbres) changée', { enabled: this.settings.autoClimbTrees });
            });
        }
    }
    
    // Mettre à jour l'affichage d'une valeur
    updateDisplayValue(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = value;
        }
    }
    
    // Mettre à jour toutes les valeurs affichées
    updateDisplayValues() {
        this.updateDisplayValue('contrast-value', this.settings.contrast + '%');
        this.updateDisplayValue('brightness-value', this.settings.brightness + '%');
        this.updateDisplayValue('saturation-value', this.settings.saturation + '%');
        this.updateDisplayValue('gamma-value', (this.settings.gamma / 100).toFixed(1));
        this.updateDisplayValue('sensitivity-value', this.settings.mouseSensitivity.toFixed(1));
        this.updateDisplayValue('fov-value', this.settings.fov + '°');
        this.updateDisplayValue('render-distance-value', this.settings.renderDistance + ' chunks');
    }
    
    // Appliquer les paramètres de couleur
    applyColorSettings() {
        const canvas = document.getElementById('game');
        if (!canvas) return;
        
        let filters = [];
        
        // Contraste
        if (this.settings.contrast !== 100) {
            filters.push(`contrast(${this.settings.contrast}%)`);
        }
        
        // Luminosité
        if (this.settings.brightness !== 100) {
            filters.push(`brightness(${this.settings.brightness}%)`);
        }
        
        // Saturation
        if (this.settings.saturation !== 100) {
            filters.push(`saturate(${this.settings.saturation}%)`);
        }
        
        // Gamma (simulé avec brightness et contrast combinés)
        if (this.settings.gamma !== 100) {
            const gammaFactor = this.settings.gamma / 100;
            if (gammaFactor !== 1.0) {
                // Simulation du gamma avec une combinaison de brightness et contrast
                const gammaBrightness = Math.pow(gammaFactor, 0.5) * 100;
                const gammaContrast = Math.pow(gammaFactor, 0.3) * 100;
                filters.push(`brightness(${gammaBrightness}%)`);
                filters.push(`contrast(${gammaContrast}%)`);
            }
        }
        
        // Mode HDR simplifié (couleurs éclatantes)
        if (this.settings.hdrMode) {
            filters.push('saturate(180%)');
            filters.push('contrast(130%)');
            filters.push('brightness(115%)');
            filters.push('drop-shadow(0 0 2px rgba(255, 255, 255, 0.3))');
        }
        
        // Correction des couleurs (ajuste la température de couleur et améliore les tons)
        if (this.settings.colorCorrection) {
            filters.push('sepia(8%)');
            filters.push('hue-rotate(3deg)');
            filters.push('contrast(105%)');
            filters.push('saturate(110%)');
        }
        
        // Appliquer les filtres
        canvas.style.filter = filters.length > 0 ? filters.join(' ') : 'none';
        
        this.logger.debug('Paramètres de couleur appliqués', {
            filters: filters,
            hdrMode: this.settings.hdrMode,
            colorCorrection: this.settings.colorCorrection
        });
    }
    
    // Appliquer la sensibilité de la souris
    applyMouseSensitivity() {
        if (window.controls) {
            window.controls.sensitivity = this.settings.mouseSensitivity * 0.001; // Conversion
            this.logger.debug('Sensibilité souris appliquée', {
                sensitivity: this.settings.mouseSensitivity
            });
        }
    }
    
    // Appliquer le FOV
    applyFOV() {
        if (window.player && window.player.camera) {
            window.player.camera.fov = this.settings.fov;
            window.player.camera.updateProjectionMatrix();
            this.logger.debug('FOV appliqué', {
                fov: this.settings.fov
            });
        }
    }
    
    // Appliquer la distance de rendu
    applyRenderDistance() {
        if (window.world) {
            window.world.renderDistance = this.settings.renderDistance;
            this.logger.debug('Distance de rendu appliquée', {
                renderDistance: this.settings.renderDistance
            });
        }
    }
    
    // Appliquer le mode de couleur Minecraft
    applyColorMode() {
        const canvas = document.getElementById('game');
        if (!canvas) return;
        
        // Supprimer les classes de mode de couleur existantes
        canvas.classList.remove('color-mode-vibrant', 'color-mode-moody', 'color-mode-classic', 'color-mode-warm', 'color-mode-cool');
        
        // Ajouter la classe de transition
        canvas.classList.add('color-mode-transition');
        
        let modeFilters = [];
        
        switch (this.settings.colorMode) {
            case 'vibrant':
                modeFilters = ['saturate(150%)', 'contrast(110%)', 'brightness(105%)'];
                canvas.classList.add('color-mode-vibrant');
                break;
                
            case 'moody':
                modeFilters = ['saturate(80%)', 'contrast(120%)', 'brightness(85%)', 'sepia(15%)'];
                canvas.classList.add('color-mode-moody');
                break;
                
            case 'classic':
                modeFilters = ['saturate(90%)', 'contrast(105%)', 'hue-rotate(-5deg)'];
                canvas.classList.add('color-mode-classic');
                break;
                
            case 'warm':
                modeFilters = ['saturate(110%)', 'brightness(105%)', 'sepia(20%)', 'hue-rotate(10deg)'];
                canvas.classList.add('color-mode-warm');
                break;
                
            case 'cool':
                modeFilters = ['saturate(110%)', 'brightness(95%)', 'hue-rotate(-15deg)', 'contrast(105%)'];
                canvas.classList.add('color-mode-cool');
                break;
                
            case 'default':
            default:
                // Pas de filtres supplémentaires pour le mode par défaut
                break;
        }
        
        // Combiner avec les paramètres de couleur existants
        this.applyColorSettings();
        
        // Ajouter les filtres du mode de couleur
        if (modeFilters.length > 0) {
            const currentFilter = canvas.style.filter || '';
            const combinedFilters = currentFilter ? `${currentFilter} ${modeFilters.join(' ')}` : modeFilters.join(' ');
            canvas.style.filter = combinedFilters;
        }
        
        this.logger.debug('Mode de couleur appliqué', {
            mode: this.settings.colorMode,
            filters: modeFilters
        });
        
        // Notification
        if (window.showNotification) {
            const modeNames = {
                'default': 'Défaut',
                'vibrant': 'Vibrant',
                'moody': 'Sombre',
                'classic': 'Classique Minecraft',
                'warm': 'Chaleureux',
                'cool': 'Froid'
            };
            window.showNotification(`🎨 Mode: ${modeNames[this.settings.colorMode]}`, 'info');
        }
    }
    
    // Appliquer les paramètres d'escalade
    applyClimbSettings() {
        if (window.player) {
            window.player.autoClimb = this.settings.autoClimb;
            window.player.autoClimbTrees = this.settings.autoClimbTrees;
            this.logger.debug('Paramètres d\'escalade appliqués', {
                autoClimb: this.settings.autoClimb,
                autoClimbTrees: this.settings.autoClimbTrees
            });
        }
    }
    
    // Appliquer tous les paramètres
    applyAllSettings() {
        this.applyColorMode();
        this.applyColorSettings();
        this.applyMouseSensitivity();
        this.applyFOV();
        this.applyRenderDistance();
        this.applyClimbSettings();
    }
    
    // Ouvrir le menu
    open() {
        const menu = document.getElementById('options-menu');
        if (menu) {
            menu.style.display = 'flex';
            this.isOpen = true;
            
            // Libérer le pointeur si verrouillé
            if (document.pointerLockElement) {
                document.exitPointerLock();
            }
            
            this.logger.info('Menu d\'options ouvert');
        }
    }
    
    // Fermer le menu
    close() {
        const menu = document.getElementById('options-menu');
        if (menu) {
            menu.style.display = 'none';
            this.isOpen = false;
            this.logger.info('Menu d\'options fermé');
        }
    }
    
    // Basculer l'état du menu
    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }
    
    // Réinitialiser aux paramètres par défaut
    resetToDefaults() {
        this.settings = { ...this.defaultSettings };
        
        // Mettre à jour tous les contrôles avec les bons IDs
        const controlMappings = {
            colorMode: 'color-mode',
            contrast: 'contrast-slider',
            brightness: 'brightness-slider', 
            saturation: 'saturation-slider',
            gamma: 'gamma-slider',
            hdrMode: 'hdr-mode',
            colorCorrection: 'color-correction',
            mouseSensitivity: 'mouse-sensitivity',
            fov: 'fov-slider',
            renderDistance: 'render-distance',
            vsync: 'vsync-mode',
            autoClimb: 'auto-climb',
            autoClimbTrees: 'auto-climb-trees'
        };
        
        Object.keys(controlMappings).forEach(key => {
            const element = document.getElementById(controlMappings[key]);
            if (element) {
                if (element.type === 'range') {
                    element.value = this.settings[key];
                } else if (element.type === 'checkbox') {
                    element.checked = this.settings[key];
                } else if (element.tagName === 'SELECT') {
                    element.value = this.settings[key];
                }
            }
        });
        
        // Mettre à jour l'affichage et appliquer
        this.updateDisplayValues();
        this.applyAllSettings();
        this.saveSettings();
        
        this.logger.info('Paramètres réinitialisés aux valeurs par défaut');
        
        // Notification
        if (window.showNotification) {
            window.showNotification('⚙️ Paramètres réinitialisés !', 'info');
        }
    }
    
    // Obtenir un paramètre spécifique
    getSetting(key) {
        return this.settings[key];
    }
    
    // Définir un paramètre spécifique
    setSetting(key, value) {
        this.settings[key] = value;
        this.saveSettings();
        this.applyAllSettings();
    }
}