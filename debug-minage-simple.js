// Script de debug simple pour le minage - à charger dans la console
console.log('🔧 Debug Minage Simple - Chargé');

// Fonction pour tester le minage manuellement
window.testMiningDebug = function() {
    console.log('🧪 === TEST DE MINAGE DEBUG ===');
    
    // 1. Vérifier les objets globaux
    console.log('📊 Vérification des objets globaux:');
    console.log('  - window.world:', !!window.world);
    console.log('  - window.player:', !!window.player);
    console.log('  - window.controls:', !!window.controls);
    console.log('  - document.pointerLockElement:', !!document.pointerLockElement);
    
    if (!window.world) {
        console.error('❌ window.world non disponible');
        return;
    }
    
    if (!window.player) {
        console.error('❌ window.player non disponible');
        return;
    }
    
    // 2. Vérifier les chunks
    console.log('🌍 Vérification des chunks:');
    console.log('  - Nombre de chunks:', window.world.chunks.size);
    
    let visibleChunks = 0;
    let chunksWithMesh = 0;
    
    window.world.chunks.forEach((chunkData, key) => {
        if (chunkData.mesh) {
            chunksWithMesh++;
            if (chunkData.mesh.visible) {
                visibleChunks++;
            }
        }
    });
    
    console.log('  - Chunks avec mesh:', chunksWithMesh);
    console.log('  - Chunks visibles:', visibleChunks);
    
    if (visibleChunks === 0) {
        console.warn('⚠️ Aucun chunk visible - le minage ne peut pas fonctionner');
        
        // Essayer de forcer la génération de meshes
        console.log('🔄 Tentative de génération forcée de meshes...');
        if (window.world.generateVisibleChunks) {
            window.world.generateVisibleChunks();
        }
        return;
    }
    
    // 3. Tester le raycast
    console.log('🎯 Test du raycast:');
    
    if (!window.player.raycaster) {
        console.error('❌ Raycaster non disponible');
        return;
    }
    
    // Récupérer tous les meshes visibles
    const allMeshes = Array.from(window.world.chunks.values())
        .filter(chunkData => chunkData && chunkData.mesh && chunkData.mesh.visible)
        .flatMap(chunkData => chunkData.mesh.children || []);
    
    console.log('  - Meshes disponibles pour raycast:', allMeshes.length);
    
    if (allMeshes.length === 0) {
        console.error('❌ Aucun mesh disponible pour le raycast');
        return;
    }
    
    // Effectuer un raycast
    const direction = window.player.camera.getWorldDirection(new THREE.Vector3());
    window.player.raycaster.set(window.player.camera.position, direction);
    const intersects = window.player.raycaster.intersectObjects(allMeshes);
    
    console.log('  - Intersections trouvées:', intersects.length);
    
    if (intersects.length > 0) {
        const intersect = intersects[0];
        console.log('  - Première intersection:', {
            point: intersect.point,
            distance: intersect.distance.toFixed(2),
            object: intersect.object.name || 'Sans nom'
        });
        
        // 4. Tester le minage
        console.log('⛏️ Test du minage:');
        console.log('  - Player.isMining:', window.player.isMining);
        console.log('  - Player.inventoryOpen:', window.player.inventoryOpen);
        
        // Forcer le démarrage du minage
        console.log('🚀 Démarrage forcé du minage...');
        window.player.startMining(window.world);
        
        setTimeout(() => {
            console.log('⏹️ Arrêt du minage...');
            window.player.stopMining();
            console.log('✅ Test de minage terminé');
        }, 2000);
        
    } else {
        console.warn('⚠️ Aucune intersection trouvée - rien à miner');
    }
};

// Fonction pour simuler un clic de minage
window.simulateClick = function() {
    console.log('🖱️ Simulation d\'un clic de minage...');
    
    // Créer un événement de clic simulé
    const mouseEvent = new MouseEvent('mousedown', {
        button: 0,
        buttons: 1,
        clientX: window.innerWidth / 2,
        clientY: window.innerHeight / 2,
        bubbles: true,
        cancelable: true
    });
    
    document.dispatchEvent(mouseEvent);
    
    setTimeout(() => {
        const mouseUpEvent = new MouseEvent('mouseup', {
            button: 0,
            buttons: 0,
            clientX: window.innerWidth / 2,
            clientY: window.innerHeight / 2,
            bubbles: true,
            cancelable: true
        });
        
        document.dispatchEvent(mouseUpEvent);
        console.log('🖱️ Simulation de clic terminée');
    }, 1000);
};

// Fonction pour forcer la génération de chunks visibles
window.forceChunkGeneration = function() {
    console.log('🌍 Forçage de la génération de chunks...');
    
    if (!window.world) {
        console.error('❌ Monde non disponible');
        return;
    }
    
    // Essayer différentes méthodes pour forcer la génération
    if (window.world.generateVisibleChunks) {
        window.world.generateVisibleChunks();
    }
    
    if (window.world.updateChunks) {
        window.world.updateChunks(window.player.camera.position);
    }
    
    if (window.world.forceChunkGeneration) {
        window.world.forceChunkGeneration();
    }
    
    console.log('✅ Tentative de génération terminée');
};

// Fonction pour afficher l'état détaillé
window.showDetailedState = function() {
    console.log('📊 === ÉTAT DÉTAILLÉ DU JEU ===');
    
    if (window.player) {
        console.log('👤 Player:');
        console.log('  - Position:', {
            x: window.player.camera.position.x.toFixed(2),
            y: window.player.camera.position.y.toFixed(2),
            z: window.player.camera.position.z.toFixed(2)
        });
        console.log('  - Velocity:', {
            x: window.player.velocity.x.toFixed(2),
            y: window.player.velocity.y.toFixed(2),
            z: window.player.velocity.z.toFixed(2)
        });
        console.log('  - onGround:', window.player.onGround);
        console.log('  - isMining:', window.player.isMining);
        console.log('  - flyMode:', window.player.flyMode);
    }
    
    if (window.controls) {
        console.log('🎮 Controls:');
        console.log('  - sensitivity:', window.controls.sensitivity);
        console.log('  - isPointerLocked:', window.controls.isPointerLocked);
    }
    
    if (window.world) {
        console.log('🌍 World:');
        console.log('  - chunks.size:', window.world.chunks.size);
        console.log('  - renderDistance:', window.world.renderDistance);
    }
    
    console.log('🖥️ Browser:');
    console.log('  - pointerLockElement:', !!document.pointerLockElement);
    console.log('  - userAgent:', navigator.userAgent.substring(0, 50) + '...');
};

// Auto-exécution pour afficher les fonctions disponibles
setTimeout(() => {
    console.log('🔧 === FONCTIONS DE DEBUG DISPONIBLES ===');
    console.log('  - testMiningDebug() : Test complet du minage');
    console.log('  - simulateClick() : Simuler un clic de minage');
    console.log('  - forceChunkGeneration() : Forcer la génération de chunks');
    console.log('  - showDetailedState() : Afficher l\'état détaillé');
    console.log('');
    console.log('💡 Commencez par: testMiningDebug()');
}, 1000);
