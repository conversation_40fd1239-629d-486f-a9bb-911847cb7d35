﻿// Three.js est chargÃ© globalement depuis le CDN
const THREE = window.THREE;

export class Player {
    constructor(scene) {
        // RÃ©cupÃ©rer le logger global
        this.logger = window.GameLogger;
        const PLAYER_VERSION = `Player-v${Date.now()}`;
        this.logger.info('Player initialisÃ©', {
            version: PLAYER_VERSION,
            features: {
                escaladeAutomatique: 'DÃ©sactivÃ©e aprÃ¨s stabilisation',
                gravite: 'DÃ©sactivÃ©e aprÃ¨s stabilisation',
                limiteInferieure: 'DÃ©sactivÃ©e aprÃ¨s stabilisation',
                logsDetailles: 'ActivÃ©s pour diagnostic'
            }
        });
        // Stocker la version pour vÃ©rification
        this.version = PLAYER_VERSION;
        // Enregistrer ce module auprÃ¨s du systÃ¨me de versioning (si disponible)
        if (window.VERSION_CONFIG && window.VERSION_CONFIG.verifyModuleIntegrity) {
            const moduleRegistry = window.VERSION_CONFIG.verifyModuleIntegrity();
            moduleRegistry.registerModule('Player', PLAYER_VERSION);
        }
        // Configuration amÃ©liorÃ©e de la camÃ©ra
        this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        // Position de dÃ©part du joueur au niveau du sol (sera ajustÃ©e automatiquement)
        this.camera.position.set(0, 100, 0); // Position temporaire haute, sera ajustÃ©e au premier update
        // PropriÃ©tÃ©s de mouvement et collision
        this.velocity = new THREE.Vector3();
        this.onGround = false;
        this.eyeHeight = 1.7; // Hauteur des yeux par rapport au sol (en blocs)
        this.playerHeight = 1.8; // Taille du joueur (1.8 blocs)
        this.stepHeight = 0.5; // Hauteur max d'escalade automatique (0.5 bloc)
        this.collisionRadius = 0.3; // Rayon de collision horizontal
        this.collisionSegments = 8; // Nombre de points de collision pour une dÃ©tection prÃ©cise
        this.needsGroundCheck = true; // Flag pour vÃ©rifier le sol au premier update
        this.flyMode = false; // Mode vol activÃ©/dÃ©sactivÃ©
        // PropriÃ©tÃ©s de saut robustes
        this.isJumping = false; // Flag pour indiquer qu'un saut est en cours
        this.jumpStartTime = 0; // Timestamp du dÃ©but du saut
        this.jumpDuration = 500; // DurÃ©e minimale d'un saut (500ms)
        // Options d'escalade (seront mises Ã  jour par OptionsManager)
        this.autoClimb = true; // Escalade automatique du terrain
        this.autoClimbTrees = false; // Escalade automatique des arbres
        // Cache pour Ã©viter les appels rÃ©pÃ©tÃ©s Ã  getGroundHeightAt
        this.lastGroundCheck = { x: null, z: null, result: null, time: 0 };
        this.groundCheckCooldown = 500; // ms entre les vÃ©rifications (augmentÃ©)
        // SystÃ¨me de stabilisation pour rÃ©duire les logs
        this.isStable = false;
        this.stableFrameCount = 0;
        this.requiredStableFrames = 60; // 1 seconde Ã  60fps
        // Protection contre l'escalade rÃ©pÃ©tÃ©e
        this.lastStepTime = 0;
        this.stepCooldown = 100; // 100ms entre les escalades
        // SystÃ¨me de minage
        this.isMining = false;
        this.miningStartTime = 0;
        this.miningTarget = null;
        this.miningProgress = 0;
        this.miningDuration = 1000; // 1 seconde pour miner un bloc
        this.reach = 5; // PortÃ©e de minage en blocs
        // Animation de bras
        this.armSwingTime = 0;
        this.armSwingDuration = 300; // 300ms par swing
        this.isSwinging = false;
        // Inventaire
        this.inventory = new Map();
        this.inventoryOpen = false;
        this.maxInventorySlots = 36; // 9x4 comme Minecraft
        // Interface utilisateur
        this.createUI();
        // SystÃ¨me de stabilisation forcÃ©e pour Ã©viter les chutes infinies
        this.lastStablePosition = { x: 0, y: 70, z: 0 };
        this.fallTime = 0;
        this.maxFallTime = 2000; // 2 secondes max de chute
        // SystÃ¨me de dÃ©sactivation de la physique aprÃ¨s stabilisation
        this.physicsEnabled = true;
        this.groundPhysicsDisabled = false;
        // Orientation initiale de la camÃ©ra (regarder horizontalement)
        this.camera.rotation.order = 'YXZ'; // Ordre de rotation important pour FPS
        this.camera.rotation.set(0, 0, 0);
        // Ajout de la camÃ©ra Ã  la scÃ¨ne
        scene.add(this.camera);
        // Debug: vÃ©rifier l'initialisation
        this.logger.info('CamÃ©ra initialisÃ©e', {
            position: {
                x: this.camera.position.x,
                y: this.camera.position.y,
                z: this.camera.position.z
            },
            rotation: {
                x: this.camera.rotation.x,
                y: this.camera.rotation.y,
                z: this.camera.rotation.z
            }
        });
        // Le redimensionnement est maintenant gÃ©rÃ© par le systÃ¨me principal dans main.js
        // Cette mÃ©thode peut Ãªtre appelÃ©e depuis main.js pour mettre Ã  jour la camÃ©ra
        this.updateCameraAspect = (width, height) => {
            this.camera.aspect = width / height;
            this.camera.updateProjectionMatrix();
            this.logger.debug('Aspect ratio de la camÃ©ra mis Ã  jour', {
                width: width,
                height: height,
                aspect: this.camera.aspect.toFixed(3),
                fov: this.camera.fov
            });
        };
    }

    update(delta, world) {
        // VÃ©rifier que delta est valide
        if (isNaN(delta) || delta <= 0) {
            delta = 0.016; // Valeur par dÃ©faut
        }
        // Logs intelligents avec le nouveau systÃ¨me
        if (window.SmartLogger) {
            if (this.needsGroundCheck || Math.abs(this.velocity.y) > 0.1) {
                window.SmartLogger.debug('PHYSICS', 'Mise Ã  jour joueur', {
                    position: {
                        x: Math.round(this.camera.position.x * 100) / 100,
                        y: Math.round(this.camera.position.y * 100) / 100,
                        z: Math.round(this.camera.position.z * 100) / 100
                    },
                    velocity: {
                        y: Math.round(this.velocity.y * 1000) / 1000
                    },
                    onGround: this.onGround,
                    needsGroundCheck: this.needsGroundCheck
                });
            }
        } else {
            // Fallback vers l'ancien systÃ¨me avec throttling
            if ((this.needsGroundCheck || Math.abs(this.velocity.y) > 0.1) && 
                (!this.lastLogTime || Date.now() - this.lastLogTime > 1000)) {
                this.logger.physics('Mise Ã  jour joueur', {
                    position: {
                        x: Math.round(this.camera.position.x * 100) / 100,
                        y: Math.round(this.camera.position.y * 100) / 100,
                        z: Math.round(this.camera.position.z * 100) / 100
                    },
                    velocity: {
                        y: Math.round(this.velocity.y * 1000) / 1000
                    },
                    onGround: this.onGround,
                    needsGroundCheck: this.needsGroundCheck
                });
                this.lastLogTime = Date.now();
            }
        }
        // Au premier update, positionner le joueur sur le sol
        if (this.needsGroundCheck && world && !this.flyMode) {
            this.logger.physics('DÃ©but du positionnement initial');
            const positioningSuccess = this.findGroundPosition(world);
            if (positioningSuccess) {
                // Marquer le positionnement initial comme terminÃ©
                this.needsGroundCheck = false;
                this.onGround = true;
                this.velocity.y = 0;
                this.logger.physics('Positionnement initial rÃ©ussi', {
                    finalPosition: {
                        x: Math.round(this.camera.position.x * 100) / 100,
                        y: Math.round(this.camera.position.y * 100) / 100,
                        z: Math.round(this.camera.position.z * 100) / 100
                    },
                    physicsEnabled: true
                });
            } else {
                // Ajouter un compteur de tentatives pour éviter les boucles infinies
                if (!this.spawnAttempts) this.spawnAttempts = 0;
                this.spawnAttempts++;

                if (this.spawnAttempts >= 5) {
                    // Trop de tentatives - forcer une position de secours
                    this.logger.warn('Trop de tentatives de spawn - position de secours forcée', {
                        attempts: this.spawnAttempts
                    });
                    this.camera.position.set(0, 70, 0);
                    this.needsGroundCheck = false;
                    this.onGround = true;
                    this.velocity.y = 0;
                } else {
                    this.logger.warn('Positionnement initial échoué', {
                        reason: 'Chunk pas encore généré',
                        action: 'Attente de génération des chunks',
                        attempt: this.spawnAttempts
                    });
                }
            }
        }
        // Position avant mouvement (pour dÃ©tection de collision)
        const previousPosition = this.camera.position.clone();
        // DÃ©composition du mouvement en Ã©tapes sÃ©parÃ©es (horizontal et vertical)
        const movement = this.velocity.clone().multiplyScalar(delta);
        // 1. Appliquer le mouvement horizontal (X et Z) d'abord
        const horizontalMovement = new THREE.Vector3(movement.x, 0, movement.z);
        this.camera.position.add(horizontalMovement);
        // 2. Appliquer le mouvement vertical AVANT la physique
        this.camera.position.y += movement.y;
        // 3. SystÃ¨me de physique robuste APRÃˆS le mouvement
        if (!this.flyMode && world) {
            const playerPos = this.camera.position;
            const feetY = playerPos.y - this.eyeHeight;
            // Essayer d'abord getGroundHeightAt
            let groundHeight = world.getGroundHeightAt(playerPos.x, playerPos.z);
            // Si getGroundHeightAt Ã©choue, chercher manuellement dans le chunk
            if (groundHeight === null || groundHeight === undefined) {
                const chunkX = Math.floor(playerPos.x / 16);
                const chunkZ = Math.floor(playerPos.z / 16);
                const chunkKey = `${chunkX},${chunkZ}`;
                if (world.chunks.has(chunkKey)) {
                    const chunk = world.chunks.get(chunkKey).chunk;
                    if (chunk && chunk.getBlockAt) {
                        const localX = Math.max(0, Math.min(15, Math.floor(playerPos.x) - chunkX * 16));
                        const localZ = Math.max(0, Math.min(15, Math.floor(playerPos.z) - chunkZ * 16));
                        // Chercher le sol de haut en bas
                        for (let y = Math.min(127, Math.floor(feetY) + 5); y >= 0; y--) {
                            try {
                                const blockType = chunk.getBlockAt(localX, y, localZ);
                                if (blockType && blockType !== 0) {
                                    groundHeight = y;
                                    break;
                                }
                            } catch (e) {
                                continue;
                            }
                        }
                    }
                }
            }
            // Appliquer la physique basÃ©e sur le sol trouvÃ©
            if (groundHeight !== null && groundHeight !== undefined) {
                const distanceToGround = feetY - (groundHeight + 1);
                const targetGroundY = groundHeight + 1 + this.eyeHeight;
                // LOGIQUE ROBUSTE : Ã‰viter les envols non dÃ©sirÃ©s
                const currentTime = Date.now();
                // VÃ©rifier si un saut est en cours
                if (this.isJumping && (currentTime - this.jumpStartTime) < this.jumpDuration) {
                    // SAUT EN COURS - Ne pas interfÃ©rer
                    this.onGround = false;
                    // Appliquer gravité réduite pendant le saut
                    if (this.velocity.y <= 0) {
                        this.velocity.y -= 8 * delta; // Gravité réduite pendant saut (réduite de 15 à 8)
                    }
                } else {
                    // Fin du saut ou pas de saut
                    this.isJumping = false;
                    if (distanceToGround <= 0.1 && this.velocity.y <= 0) {
                        // ATTERRISSAGE FORCÃ‰ - Stabilisation immÃ©diate
                        this.camera.position.y = targetGroundY;
                        this.velocity.y = 0;
                        this.onGround = true;
                        this.fallTime = 0;
                        // Sauvegarder position stable
                        this.lastStablePosition = {
                            x: playerPos.x,
                            y: targetGroundY,
                            z: playerPos.z
                        };
                    } else if (distanceToGround > 0.1 && this.velocity.y <= 0) {
                        // EN CHUTE - Appliquer gravité normale (réduite)
                        this.onGround = false;
                        this.velocity.y -= 12 * delta; // Gravité normale (réduite de 20 à 12)
                        if (this.velocity.y < -15) this.velocity.y = -15; // Vitesse de chute limitée (réduite de -20 à -15)
                        // Protection contre chute infinie
                        this.fallTime += delta * 1000;
                        if (this.fallTime > 3000) {
                            // Repositionnement d'urgence
                            this.camera.position.y = targetGroundY;
                            this.velocity.y = 0;
                            this.onGround = true;
                            this.fallTime = 0;
                            this.logger.warn('Repositionnement d\'urgence - chute infinie');
                        }
                    } else if (this.velocity.y > 0 && !this.isJumping) {
                        // MOUVEMENT VERTICAL NON AUTORISÉ - Corriger (gravité réduite)
                        this.onGround = false;
                        this.velocity.y -= 15 * delta; // Gravité forte pour corriger (réduite de 25 à 15)
                    }
                }
            } else {
                // Pas de sol détecté - gravité avec protection renforcée (réduite)
                this.onGround = false;
                this.velocity.y -= 18 * delta; // Gravité réduite (de 30 à 18)
                if (this.velocity.y < -20) this.velocity.y = -20; // Vitesse max réduite (de -30 à -20)
                // Protection contre chute infinie
                this.fallTime += delta * 1000;
                if (this.fallTime > 3000) {
                    // Repositionnement d'urgence vers position stable ou origine
                    if (this.lastStablePosition && this.lastStablePosition.y > 0) {
                        this.camera.position.copy(this.lastStablePosition);
                    } else {
                        this.camera.position.set(0, 70, 0);
                    }
                    this.velocity.y = 0;
                    this.onGround = true;
                    this.fallTime = 0;
                    this.logger.warn('Repositionnement d\'urgence effectuÃ©');
                }
            }
        } else if (!this.flyMode) {
            // Mode non-vol sans monde - gravité simple (réduite)
            this.velocity.y -= 18 * delta; // Gravité réduite (de 30 à 18)
            if (this.velocity.y < -20) this.velocity.y = -20; // Vitesse max réduite (de -30 à -20)
        }
        // 4. Collision horizontale simplifiÃ©e - permettre mouvement normal
        if (!this.flyMode && world && world.hasCollisionAt) {
            const playerPos = this.camera.position;
            const radius = this.collisionRadius;
            const feetY = playerPos.y - this.eyeHeight;
            // VÃ©rifier collision Ã  hauteur de poitrine (plus haut pour Ã©viter dÃ©tection sur terrain plat)
            const chestY = feetY + 1.3; // Hauteur de la poitrine du joueur
            const hasChestCollision = world.hasCollisionAt(playerPos.x, chestY, playerPos.z, radius);
            if (hasChestCollision) {
                // Obstacle au niveau de la poitrine - vÃ©rifier si on peut escalader
                let canClimb = false;
                if (this.onGround && Math.abs(this.velocity.y) < 0.1) {
                    // Obtenir le type de bloc qui bloque
                    const blockY = Math.floor(chestY);
                    const blockType = world.getBlockTypeAt(playerPos.x, blockY, playerPos.z);
                    // VÃ©rifier si l'escalade est autorisÃ©e selon les paramÃ¨tres
                    let shouldClimb = false;
                    if (world.textureGenerator && world.textureGenerator.isTreeBlock(blockType)) {
                        // C'est un bloc d'arbre - vÃ©rifier si l'escalade d'arbres est activÃ©e
                        shouldClimb = this.autoClimbTrees;
                        this.logger.debug('Bloc d\'arbre dÃ©tectÃ©', {
                            blockType: blockType,
                            autoClimbTrees: this.autoClimbTrees,
                            willClimb: shouldClimb
                        });
                    } else if (world.isClimbableBlock && world.isClimbableBlock(blockType)) {
                        // C'est un bloc de terrain - vÃ©rifier si l'escalade de terrain est activÃ©e
                        shouldClimb = this.autoClimb;
                        this.logger.debug('Bloc de terrain dÃ©tectÃ©', {
                            blockType: blockType,
                            autoClimb: this.autoClimb,
                            willClimb: shouldClimb
                        });
                    }
                    if (shouldClimb) {
                        // VÃ©rifier si on peut monter d'1 bloc (pas un mur de 2+ blocs)
                        const aboveChestY = feetY + 2.3;
                        const hasAboveCollision = world.hasCollisionAt(playerPos.x, aboveChestY, playerPos.z, radius);
                        if (!hasAboveCollision) {
                            canClimb = true;
                        }
                    }
                }
                if (canClimb) {
                    // Escalade automatique selon les paramÃ¨tres
                    const now = performance.now();
                    if (now - this.lastStepTime > this.stepCooldown) {
                        this.camera.position.y += 1.0;
                        this.velocity.y = 0;
                        this.lastStepTime = now;
                        const blockType = world.getBlockTypeAt(playerPos.x, Math.floor(chestY), playerPos.z);
                        const isTree = world.textureGenerator && world.textureGenerator.isTreeBlock(blockType);
                        this.logger.physics('Escalade effectuÃ©e', {
                            blockType: blockType,
                            isTree: isTree,
                            position: { x: playerPos.x, y: this.camera.position.y, z: playerPos.z }
                        });
                    }
                } else {
                    // Obstacle non-grimpable - bloquer le mouvement
                    this.camera.position.x = previousPosition.x;
                    this.camera.position.z = previousPosition.z;
                    this.velocity.x = 0;
                    this.velocity.z = 0;
                    const blockType = world.getBlockTypeAt(playerPos.x, Math.floor(chestY), playerPos.z);
                    const isTree = world.textureGenerator && world.textureGenerator.isTreeBlock(blockType);
                    this.logger.debug('Mouvement bloquÃ© par obstacle', {
                        blockType: blockType,
                        isTree: isTree,
                        reason: isTree ? 'Escalade d\'arbres dÃ©sactivÃ©e' : 'Bloc non-grimpable'
                    });
                }
            }
            // Pas de collision au niveau de la poitrine = mouvement libre
        }
        // 5. Collision avec le plafond seulement (le mouvement Y a dÃ©jÃ  Ã©tÃ© appliquÃ©)
        if (!this.flyMode && world && world.hasCollisionAt) {
            const playerPos = this.camera.position;
            const radius = this.collisionRadius;
            // Collision avec le plafond
            if (this.velocity.y > 0 && world.hasCollisionAt(playerPos.x, playerPos.y + 0.5, playerPos.z, radius)) {
                // Revenir Ã  la position prÃ©cÃ©dente et arrÃªter le mouvement vertical
                this.camera.position.y = previousPosition.y;
                this.velocity.y = 0;
            }
        }
        // Collision avec le sol du monde (limite infÃ©rieure) seulement si pas en mode vol
        if (!this.flyMode) {
            const minGroundLevel = this.eyeHeight;
            if (this.camera.position.y < minGroundLevel) {
                this.camera.position.y = minGroundLevel;
                this.velocity.y = 0;
                this.onGround = true;
            }
        }
        // Friction au sol
        if (this.onGround) {
            this.velocity.x *= 0.9;
            this.velocity.z *= 0.9;
        }
        // Limiter la vitesse horizontale maximale
        const maxSpeed = 20;
        const currentSpeed = Math.sqrt(this.velocity.x * this.velocity.x + this.velocity.z * this.velocity.z);
        if (currentSpeed > maxSpeed) {
            const scale = maxSpeed / currentSpeed;
            this.velocity.x *= scale;
            this.velocity.z *= scale;
        }
        // Debug: vÃ©rifier que la position est valide
        if (isNaN(this.camera.position.x) || isNaN(this.camera.position.y) || isNaN(this.camera.position.z)) {
            this.logger.error('Position camÃ©ra invalide dÃ©tectÃ©e', {
                invalidPosition: {
                    x: this.camera.position.x,
                    y: this.camera.position.y,
                    z: this.camera.position.z
                },
                action: 'RÃ©initialisation Ã  la position de spawn'
            });
            this.camera.position.set(0, this.eyeHeight, 0);
        }
        // Mettre Ã  jour le systÃ¨me de minage
        this.updateMining(delta);
    }

    // MÃ©thode pour trouver la position du sol au dÃ©marrage (spawn dans les plaines)
    findGroundPosition(world) {
        // Utiliser une position de spawn plus proche de l'origine pour Ã©viter les chunks non gÃ©nÃ©rÃ©s
        if (!this.spawnPosition) {
            // Chercher d'abord dans les chunks dÃ©jÃ  gÃ©nÃ©rÃ©s
            const availableChunks = Array.from(world.chunks.keys());
            if (availableChunks.length > 0) {
                // Utiliser le premier chunk disponible proche de l'origine
                const centerChunks = availableChunks.filter(key => {
                    const [x, z] = key.split(',').map(Number);
                    return Math.abs(x) <= 2 && Math.abs(z) <= 2; // Chunks proches de l'origine
                });
                if (centerChunks.length > 0) {
                    const [chunkX, chunkZ] = centerChunks[0].split(',').map(Number);
                    this.spawnPosition = {
                        x: chunkX * 16 + 8, // Centre du chunk
                        z: chunkZ * 16 + 8
                    };
                } else {
                    // Utiliser le premier chunk disponible
                    const [chunkX, chunkZ] = availableChunks[0].split(',').map(Number);
                    this.spawnPosition = {
                        x: chunkX * 16 + 8,
                        z: chunkZ * 16 + 8
                    };
                }
            } else {
                // Fallback Ã  l'origine
                this.spawnPosition = { x: 0, z: 0 };
            }
        }
        const startX = this.spawnPosition.x;
        const startZ = this.spawnPosition.z;
        this.logger.debug('Recherche position de spawn', {
            targetPosition: { x: startX, z: startZ },
            currentY: this.camera.position.y
        });
        // VÃ©rifier si le chunk central existe
        const chunkX = Math.floor(startX / 16);
        const chunkZ = Math.floor(startZ / 16);
        const chunkKey = `${chunkX},${chunkZ}`;
        if (!world.chunks.has(chunkKey)) {
            this.logger.debug('Chunk de spawn pas encore gÃ©nÃ©rÃ©', {
                chunkKey: chunkKey,
                availableChunks: Array.from(world.chunks.keys())
            });
            return false; // Ã‰CHEC - Attendre que le chunk soit gÃ©nÃ©rÃ©
        }
        // Utiliser la nouvelle mÃ©thode spÃ©cialisÃ©e pour trouver le sol
        const groundHeight = world.getGroundHeightAt(startX, startZ);
        if (groundHeight !== null && groundHeight > 0) {
            const newY = groundHeight + 1 + this.eyeHeight;
            // Positionner le joueur sur le sol
            this.camera.position.x = startX;
            this.camera.position.y = newY;
            this.camera.position.z = startZ;
            this.velocity.y = 0;
            this.onGround = true;
            this.fallTime = 0; // Reset du timer de chute
            // Sauvegarder comme position stable
            this.lastStablePosition = {
                x: startX,
                y: newY,
                z: startZ
            };
            this.logger.info('Spawn rÃ©ussi', {
                spawnPosition: { x: startX, y: newY, z: startZ },
                groundHeight: groundHeight,
                chunkKey: chunkKey
            });
            return true; // SUCCÃˆS - Positionnement rÃ©ussi
        }
        // Si aucun sol trouvÃ©, chercher manuellement dans le chunk
        const chunk = world.chunks.get(chunkKey);
        if (chunk && chunk.chunk) {
            const localX = Math.floor(startX) - chunkX * 16;
            const localZ = Math.floor(startZ) - chunkZ * 16;
            // Chercher le sol de haut en bas
            for (let y = 127; y >= 1; y--) {
                const blockType = chunk.chunk.getBlockAt(localX, y, localZ);
                if (blockType !== 0) { // Bloc solide trouvÃ©
                    const newY = y + 1 + this.eyeHeight;
                    this.camera.position.x = startX;
                    this.camera.position.y = newY;
                    this.camera.position.z = startZ;
                    this.velocity.y = 0;
                    this.onGround = true;
                    this.fallTime = 0;
                    this.lastStablePosition = {
                        x: startX,
                        y: newY,
                        z: startZ
                    };
                    this.logger.info('Sol trouvÃ© manuellement', {
                        position: { x: startX, y: newY, z: startZ },
                        groundY: y,
                        blockType: blockType
                    });
                    return true;
                }
            }
        }
        // Si vraiment aucun sol trouvÃ©, utiliser une hauteur par dÃ©faut
        this.camera.position.x = startX;
        this.camera.position.y = 70;
        this.camera.position.z = startZ;
        this.velocity.y = 0;
        this.onGround = true; // Forcer au sol pour Ã©viter la chute
        this.fallTime = 0;
        this.logger.warn('Sol non trouvÃ©, position par dÃ©faut utilisÃ©e', {
            defaultPosition: { x: startX, y: 70, z: startZ }
        });
        return true; // SUCCÃˆS - Position par dÃ©faut utilisÃ©e
    }

    // Trouver une position de spawn dans un biome de plaines
    findPlainsSpawnPosition(world) {
        if (!world.generator) {
            return { x: 0, z: 0 }; // Position par dÃ©faut si pas de gÃ©nÃ©rateur
        }
        // Chercher dans un rayon autour de l'origine
        const searchRadius = 100;
        const maxAttempts = 50;
        for (let attempt = 0; attempt < maxAttempts; attempt++) {
            const angle = (attempt / maxAttempts) * Math.PI * 2;
            const distance = (attempt / maxAttempts) * searchRadius;
            const x = Math.floor(Math.cos(angle) * distance);
            const z = Math.floor(Math.sin(angle) * distance);
            const biomeValue = world.generator.getBiomeValue(x, z);
            const biome = world.generator.getBiome(biomeValue);
            // Chercher spÃ©cifiquement les plaines
            if (biome.name === 'Plains') {
                this.logger.info('Position de spawn trouvÃ©e dans les plaines', {
                    position: { x, z },
                    biome: biome.name,
                    attempt: attempt + 1
                });
                return { x, z };
            }
        }
        // Si aucune plaine trouvÃ©e, utiliser la position par dÃ©faut
        this.logger.warn('Aucune plaine trouvÃ©e, utilisation position par dÃ©faut');
        return { x: 0, z: 0 };
    }

    // MÃ©thode pour trouver le sol en dessous du joueur avec cache amÃ©liorÃ©
    findGroundBelow(world, x, currentY, z) {
        const now = performance.now();
        // Utiliser une grille plus large pour le cache (blocs de 4x4)
        const cacheX = Math.floor(x / 4) * 4;
        const cacheZ = Math.floor(z / 4) * 4;
        // VÃ©rifier le cache si on est dans la mÃªme zone et que le cooldown n'est pas Ã©coulÃ©
        if (this.lastGroundCheck.x === cacheX &&
            this.lastGroundCheck.z === cacheZ &&
            now - this.lastGroundCheck.time < this.groundCheckCooldown) {
            // Utiliser le rÃ©sultat en cache
            const cachedResult = this.lastGroundCheck.result;
            if (cachedResult !== null && cachedResult <= currentY + 0.5) {
                return cachedResult;
            }
            return null;
        }
        // Faire un nouvel appel Ã  getGroundHeightAt
        const groundHeight = world.getGroundHeightAt(x, z);
        // Mettre Ã  jour le cache seulement si on a un rÃ©sultat valide
        if (groundHeight !== null) {
            this.lastGroundCheck = {
                x: cacheX,
                z: cacheZ,
                result: groundHeight,
                time: now
            };
        }
        if (groundHeight !== null && groundHeight <= currentY + 0.5) {
            return groundHeight; // Retourner la hauteur du bloc solide
        }
        return null; // Aucun sol trouvÃ©
    }

    // MÃ©thode pour vÃ©rifier si un bloc est visible par le joueur
    canSeeBlock(blockX, blockY, blockZ, maxDistance = 100) {
        const position = this.camera.position;
        const distance = Math.sqrt(
            Math.pow(blockX - position.x, 2) +
            Math.pow(blockY - position.y, 2) +
            Math.pow(blockZ - position.z, 2)
        );
        // VÃ©rifier la distance maximale
        return distance <= maxDistance;
    }

    // ===== MÃ‰THODES DE SECOURS =====
    // MÃ©thode pour tÃ©lÃ©porter le joueur sur la surface (fonction de secours)
    emergencySpawnOnSurface(world) {
        this.logger.warn('Spawn d\'urgence activÃ©');
        const currentX = this.camera.position.x;
        const currentZ = this.camera.position.z;
        // Essayer de trouver le sol Ã  la position actuelle
        const groundHeight = world.getGroundHeightAt(currentX, currentZ);
        if (groundHeight !== null) {
            const newY = groundHeight + 1 + this.eyeHeight;
            this.camera.position.y = newY;
            this.velocity.y = 0;
            this.onGround = true;
            this.logger.warn('Spawn d\'urgence rÃ©ussi', { position: { x: currentX, y: newY, z: currentZ } });
            return true;
        } else {
            // Position de secours au spawn par dÃ©faut
            this.camera.position.set(0, 70, 0);
            this.velocity.y = 0;
            this.onGround = true;
            this.logger.warn('Spawn de secours utilisÃ©', { position: { x: 0, y: 70, z: 0 } });
            return false;
        }
    }

    // MÃ©thode pour rÃ©initialiser complÃ¨tement la physique du joueur
    resetAllPhysics() {
        this.logger.info('RÃ©initialisation complÃ¨te de la physique');
        this.needsGroundCheck = false;
        this.isStable = false;
        this.stableFrameCount = 0;
        this.fallTime = 0;
        this.velocity.set(0, 0, 0);
        this.onGround = false;
        this.logger.info('Physique rÃ©initialisÃ©e');
    }

    // ===== SYSTÃˆME DE MINAGE =====
    // CrÃ©er l'interface utilisateur
    createUI() {
        // Le crosshair est dÃ©jÃ  dÃ©fini dans le HTML, pas besoin de le recrÃ©er
        // CrÃ©er l'interface d'inventaire
        this.createInventoryUI();
        // CrÃ©er la barre de progression de minage
        this.createMiningProgressBar();
        // CrÃ©er la hotbar (barre d'outils)
        this.createHotbar();
    }

    // CrÃ©er la barre de progression de minage
    createMiningProgressBar() {
        const progressContainer = document.getElementById('mining-progress') || document.createElement('div');
        if (!document.getElementById('mining-progress')) {
            progressContainer.id = 'mining-progress';
            progressContainer.style.cssText = `
                position: fixed;
                top: 60%;
                left: 50%;
                width: 200px;
                height: 20px;
                transform: translateX(-50%);
                background: rgba(0, 0, 0, 0.5);
                border: 2px solid white;
                border-radius: 10px;
                display: none;
                z-index: 1000;
            `;
            const progressBar = document.createElement('div');
            progressBar.id = 'mining-progress-bar';
            progressBar.style.cssText = `
                width: 0%;
                height: 100%;
                background: linear-gradient(90deg, #4CAF50, #8BC34A);
                border-radius: 8px;
                transition: width 0.1s ease;
            `;
            progressContainer.appendChild(progressBar);
            document.body.appendChild(progressContainer);
        }
    }

    // CrÃ©er la hotbar
    createHotbar() {
        const hotbar = document.getElementById('hotbar') || document.createElement('div');
        if (!document.getElementById('hotbar')) {
            hotbar.id = 'hotbar';
            hotbar.style.cssText = `
                position: fixed;
                bottom: 20px;
                left: 50%;
                transform: translateX(-50%);
                display: flex;
                gap: 2px;
                background: rgba(0, 0, 0, 0.5);
                padding: 5px;
                border-radius: 5px;
                z-index: 1000;
            `;
            // CrÃ©er 9 slots pour la hotbar
            for (let i = 0; i < 9; i++) {
                const slot = document.createElement('div');
                slot.className = 'hotbar-slot';
                slot.dataset.slot = i;
                slot.style.cssText = `
                    width: 40px;
                    height: 40px;
                    background: rgba(255, 255, 255, 0.1);
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    border-radius: 3px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 12px;
                    cursor: pointer;
                `;
                if (i === 0) {
                    slot.style.border = '2px solid white'; // Slot sÃ©lectionnÃ©
                }
                hotbar.appendChild(slot);
            }
            document.body.appendChild(hotbar);
            this.selectedSlot = 0;
        }
    }

    // CrÃ©er l'interface d'inventaire
    createInventoryUI() {
        const inventoryContainer = document.getElementById('inventory') || document.createElement('div');
        if (!document.getElementById('inventory')) {
            inventoryContainer.id = 'inventory';
            inventoryContainer.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 400px;
                height: 300px;
                background: rgba(0, 0, 0, 0.8);
                border: 2px solid white;
                border-radius: 10px;
                padding: 20px;
                display: none;
                z-index: 2000;
            `;
            const title = document.createElement('h3');
            title.textContent = 'Inventaire';
            title.style.cssText = `
                color: white;
                margin: 0 0 15px 0;
                text-align: center;
            `;
            const grid = document.createElement('div');
            grid.id = 'inventory-grid';
            grid.style.cssText = `
                display: grid;
                grid-template-columns: repeat(9, 1fr);
                gap: 2px;
                height: 200px;
            `;
            // CrÃ©er 36 slots d'inventaire (4 rangÃ©es de 9)
            for (let i = 0; i < 36; i++) {
                const slot = document.createElement('div');
                slot.className = 'inventory-slot';
                slot.dataset.slot = i;
                slot.style.cssText = `
                    background: rgba(255, 255, 255, 0.1);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 3px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 10px;
                    cursor: pointer;
                    min-height: 35px;
                `;
                grid.appendChild(slot);
            }
            inventoryContainer.appendChild(title);
            inventoryContainer.appendChild(grid);
            document.body.appendChild(inventoryContainer);
        }
    }

    // Commencer le minage
    startMining(world = null) {
        this.logger.mining('🔨 startMining appelé', {
            isMining: this.isMining,
            inventoryOpen: this.inventoryOpen,
            worldProvided: !!world,
            windowWorldExists: !!window.world
        });

        if (this.isMining || this.inventoryOpen) {
            this.logger.mining('❌ Minage bloqué', {
                reason: this.isMining ? 'déjà en cours' : 'inventaire ouvert'
            });
            return;
        }

        // Utiliser le monde global si aucun monde n'est passé en paramètre
        const worldToUse = world || window.world;
        if (!worldToUse) {
            this.logger.warn('❌ Aucun monde disponible pour le minage');
            return;
        }

        this.logger.mining('🎯 Recherche de bloc cible...');
        const target = this.getTargetBlock(worldToUse);
        if (!target) {
            this.logger.mining('❌ Aucun bloc ciblé pour le minage');
            return;
        }

        this.isMining = true;
        this.miningTarget = target;
        this.miningStartTime = performance.now();
        this.miningProgress = 0;

        // Afficher la barre de progression
        const progressContainer = document.getElementById('mining-progress');
        if (progressContainer) {
            progressContainer.style.display = 'block';
        }

        // Commencer l'animation de bras
        this.startArmSwing();
        this.logger.mining('Début du minage', {
            target,
            position: { x: target.x, y: target.y, z: target.z },
            blockType: target.blockType
        });
    }

    // ArrÃªter le minage
    stopMining() {
        if (!this.isMining) return;
        this.isMining = false;
        this.miningTarget = null;
        this.miningProgress = 0;
        // Cacher la barre de progression
        const progressContainer = document.getElementById('mining-progress');
        if (progressContainer) {
            progressContainer.style.display = 'none';
        }
        // ArrÃªter l'animation de bras
        this.stopArmSwing();
        this.logger.info('Minage arrÃªtÃ©');
    }

    // Obtenir le bloc visÃ©
    getTargetBlock(world) {
        this.logger.mining('🎯 getTargetBlock appelé', {
            worldChunks: world.chunks ? world.chunks.size : 0,
            cameraPosition: {
                x: this.camera.position.x.toFixed(2),
                y: this.camera.position.y.toFixed(2),
                z: this.camera.position.z.toFixed(2)
            }
        });
        // RÃ©cupÃ©ration des meshes Ã  tester
        // Utiliser le monde passé en paramètre au lieu de window.world
        const allMeshes = Array.from(world.chunks.values())
            .filter(chunkData => chunkData && chunkData.mesh && chunkData.mesh.visible)
            .flatMap(chunkData => chunkData.mesh.children || []);
        // Variable chunkMeshes n'existe plus, on utilise allMeshes
        this.logger.debug('Nombre de meshes Ã  tester', { count: allMeshes.length });

        // Vérifier qu'il y a des meshes à tester
        if (allMeshes.length === 0) {
            this.logger.mining('❌ Aucun mesh disponible pour le raycast');
            return null;
        }

        this.logger.mining('📊 Meshes trouvés pour raycast', { count: allMeshes.length });

        // Direction du raycast
        const direction = this.camera.getWorldDirection(new THREE.Vector3());
        this.logger.mining('🎯 Direction du raycast', {
            origin: {
                x: this.camera.position.x.toFixed(2),
                y: this.camera.position.y.toFixed(2),
                z: this.camera.position.z.toFixed(2)
            },
            direction: {
                x: direction.x.toFixed(3),
                y: direction.y.toFixed(3),
                z: direction.z.toFixed(3)
            }
        });
        this.raycaster.set(this.camera.position, direction);
        const intersects = this.raycaster.intersectObjects(allMeshes);
        this.logger.mining('📊 Résultat du raycast', {
            intersectsCount: intersects.length,
            hasIntersections: intersects.length > 0
        });
        if (intersects.length > 0) {
            this.logger.info('Bloc touchÃ© par le raycast', { intersect: intersects[0] });
            // ... suppression du bloc ...
        } else {
            this.logger.warn('Aucun bloc touchÃ© par le raycast');
        }
        // Chercher le bloc le plus proche dans la portÃ©e
        for (let distance = 1; distance <= this.reach; distance += 0.5) {
            const testPos = this.camera.position.clone().add(direction.clone().multiplyScalar(distance));
            const blockX = Math.floor(testPos.x);
            const blockY = Math.floor(testPos.y);
            const blockZ = Math.floor(testPos.z);
            // VÃ©rifier si il y a un bloc Ã  cette position
            const blockType = world?.getBlockTypeAt?.(blockX, blockY, blockZ);
            if (blockType && blockType !== 0) { // 0 = air
                return {
                    x: blockX,
                    y: blockY,
                    z: blockZ,
                    blockType: blockType,
                    distance: distance
                };
            }
        }
        return null;
    }

    // Commencer l'animation de bras
    startArmSwing() {
        this.isSwinging = true;
        this.armSwingTime = 0;
    }

    // ArrÃªter l'animation de bras
    stopArmSwing() {
        this.isSwinging = false;
        this.armSwingTime = 0;
    }

    // Mettre Ã  jour le systÃ¨me de minage
    updateMining(delta) {
        if (!this.isMining || !this.miningTarget) return;
        const now = performance.now();
        const elapsed = now - this.miningStartTime;
        this.miningProgress = Math.min(elapsed / this.miningDuration, 1.0);
        // Mettre Ã  jour la barre de progression
        const progressBar = document.getElementById('mining-progress-bar');
        if (progressBar) {
            progressBar.style.width = `${this.miningProgress * 100}%`;
        }
        // VÃ©rifier si le minage est terminÃ©
        if (this.miningProgress >= 1.0) {
            this.completeMining();
        }
        // Mettre Ã  jour l'animation de bras
        if (this.isSwinging) {
            this.armSwingTime += delta * 1000;
            if (this.armSwingTime >= this.armSwingDuration) {
                this.armSwingTime = 0;
            }
        }
    }

    // Terminer le minage
    completeMining() {
        if (!this.miningTarget) return;
        const { x, y, z, blockType } = this.miningTarget;
        // DÃ©truire le bloc dans le monde
        if (window.world && window.world.setBlockAt) {
            window.world.setBlockAt(x, y, z, 0); // 0 = air
        }
        // Ajouter le bloc Ã  l'inventaire
        this.addToInventory(blockType, 1);
        this.logger.mining('Bloc miné avec succès', { blockType, position: { x, y, z } });
        // ArrÃªter le minage
        this.stopMining();
    }

    // ===== SYSTÃˆME D'INVENTAIRE =====
    // Ajouter un objet Ã  l'inventaire
    addToInventory(blockType, quantity = 1) {
        const blockName = this.getBlockName(blockType);
        if (this.inventory.has(blockType)) {
            this.inventory.set(blockType, this.inventory.get(blockType) + quantity);
        } else {
            this.inventory.set(blockType, quantity);
        }
        this.logger.info('AjoutÃ© Ã  l\'inventaire', { blockType, blockName, quantity });
        this.updateInventoryUI();
        this.updateHotbarUI();
    }

    // Obtenir le nom d'un bloc
    getBlockName(blockType) {
        const blockNames = {
            1: 'Pierre',
            2: 'Terre',
            3: 'Herbe',
            4: 'Sable',
            6: 'Bois',
            7: 'Feuilles',
            8: 'Neige',
            9: 'Glace',
            10: 'Argile',
            11: 'Gravier',
            12: 'Pierre TaillÃ©e',
            13: 'Bedrock',
            14: 'Minerai de Charbon',
            15: 'Minerai de Fer',
            16: 'Minerai d\'Or',
            17: 'Minerai de Diamant',
            18: 'Bois de ChÃªne',
            19: 'Bois de Bouleau',
            20: 'Bois de Pin',
            21: 'Feuilles de ChÃªne',
            22: 'Feuilles de Bouleau',
            23: 'Feuilles de Pin',
            24: 'Cactus',
            25: 'Herbe Haute',
            26: 'Fleurs',
            27: 'Champignon',
            28: 'Lave'
        };
        return blockNames[blockType] || `Bloc ${blockType}`;
    }

    // Mettre Ã  jour l'interface d'inventaire
    updateInventoryUI() {
        const slots = document.querySelectorAll('.inventory-slot');
        let slotIndex = 0;
        // Vider tous les slots
        slots.forEach(slot => {
            slot.textContent = '';
            slot.style.background = 'rgba(255, 255, 255, 0.1)';
        });
        // Remplir avec les objets de l'inventaire
        for (const [blockType, quantity] of this.inventory) {
            if (slotIndex >= slots.length) break;
            const slot = slots[slotIndex];
            const blockName = this.getBlockName(blockType);
            slot.textContent = `${blockName}
${quantity}`;
            slot.style.background = 'rgba(100, 150, 200, 0.3)';
            slot.dataset.blockType = blockType;
            slot.dataset.quantity = quantity;
            slotIndex++;
        }
    }

    // Mettre Ã  jour la hotbar
    updateHotbarUI() {
        const slots = document.querySelectorAll('.hotbar-slot');
        let slotIndex = 0;
        // Vider tous les slots
        slots.forEach(slot => {
            slot.textContent = '';
        });
        // Remplir avec les premiers objets de l'inventaire
        for (const [blockType, quantity] of this.inventory) {
            if (slotIndex >= 9) break;
            const slot = slots[slotIndex];
            const blockName = this.getBlockName(blockType);
            slot.textContent = `${blockName.substring(0, 3)}
${quantity}`;
            slot.dataset.blockType = blockType;
            slot.dataset.quantity = quantity;
            slotIndex++;
        }
    }

    // Toggle inventaire
    toggleInventory() {
        this.inventoryOpen = !this.inventoryOpen;
        const inventory = document.getElementById('inventory');
        if (inventory) {
            inventory.style.display = this.inventoryOpen ? 'block' : 'none';
        }
        this.logger.info('Inventaire', { action: this.inventoryOpen ? 'ouvert' : 'fermÃ©' });
    }
}
