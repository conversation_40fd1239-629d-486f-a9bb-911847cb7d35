// Three.js est chargé globalement depuis le CDN
const THREE = window.THREE;

export class Controls {
    constructor(player, domElement) {
        // Récupérer le logger global
        this.logger = window.GameLogger;

        this.player = player;
        this.domElement = domElement;
        this.keys = {};

        // Euler angles pour une rotation plus naturelle
        this.eulerX = 0;
        this.eulerY = 0;

        // Sensibilité ajustée pour une meilleure réponse
        this.sensitivity = 0.002;
        this.isPointerLocked = false;

        // Système de contrôle de saut pour éviter les boucles infinies
        this.jumpPressed = false;
        this.lastJumpTime = 0;
        this.jumpCooldown = 200; // 200ms entre les sauts

        this.logger.info('Contrôles initialisés', {
            sensitivity: this.sensitivity,
            domElement: domElement.tagName
        });

        // Contrôles clavier
        domElement.addEventListener('keydown', e => this.keys[e.code] = true);
        domElement.addEventListener('keyup', e => this.keys[e.code] = false);

        // Contrôles souris
        domElement.addEventListener('click', () => {
            this.logger.info('Click détecté, demande de verrouillage du pointeur');
            domElement.requestPointerLock();
        });

        // Gestion du verrouillage du pointeur
        document.addEventListener('pointerlockchange', () => {
            this.isPointerLocked = document.pointerLockElement === domElement;
            this.logger.info('État du verrouillage du pointeur changé', {
                isLocked: this.isPointerLocked,
                element: domElement.tagName
            });

            // Masquer les instructions quand le pointeur est verrouillé
            const instructions = document.getElementById('instructions');
            if (instructions) {
                instructions.style.display = this.isPointerLocked ? 'none' : 'block';
            }
        });

        // Mouvement de la souris avec système de rotation direct plus stable
        document.addEventListener('mousemove', (e) => {
            if (this.isPointerLocked && e.movementX !== undefined && e.movementY !== undefined) {
                // Sensibilité réduite et fixée pour stabiliser la rotation
                const fixedSensitivity = 0.002;

                // Rotation horizontale (autour de l'axe Y)
                this.eulerY -= e.movementX * fixedSensitivity;

                // Rotation verticale (autour de l'axe X)
                this.eulerX -= e.movementY * fixedSensitivity;

                // Limiter plus strictement la rotation verticale
                this.eulerX = Math.max(-Math.PI / 2 + 0.2, Math.min(Math.PI / 2 - 0.2, this.eulerX));

                // Rotation directe pour plus de stabilité
                this.player.camera.rotation.order = 'YXZ';
                this.player.camera.rotation.x = this.eulerX;
                this.player.camera.rotation.y = this.eulerY;
            }
        });

        // Gestion de la sortie du verrouillage
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Escape' && this.isPointerLocked) {
                document.exitPointerLock();
            }
        });

        // Gestion du redimensionnement
        window.addEventListener('resize', () => {
            this.player.camera.aspect = window.innerWidth / window.innerHeight;
            this.player.camera.updateProjectionMatrix();
        });

        // Événements de souris pour le minage
        document.addEventListener('mousedown', (e) => {
            if (this.isPointerLocked && e.button === 0) { // Clic gauche
                e.preventDefault(); // Empêcher les comportements par défaut
                this.logger.mining('Début du minage (clic gauche)', {
                    button: e.button,
                    pointerLocked: this.isPointerLocked
                });
                // Passer le monde global au minage
                this.player.startMining(window.world);
            }
        });

        document.addEventListener('mouseup', (e) => {
            if (this.isPointerLocked && e.button === 0) { // Relâchement clic gauche
                e.preventDefault(); // Empêcher les comportements par défaut
                this.logger.mining('Arrêt du minage (relâchement clic)', {
                    button: e.button,
                    pointerLocked: this.isPointerLocked
                });
                this.player.stopMining();
            }
        });
    }

    // Méthode dédiée pour mettre à jour la rotation de la caméra (simplifiée)
    updateCameraRotation() {
        // Application directe des angles sans utiliser de quaternion
        this.player.camera.rotation.order = 'YXZ';
        this.player.camera.rotation.x = this.eulerX;
        this.player.camera.rotation.y = this.eulerY;
        this.player.camera.rotation.z = 0;
    }

    update(delta) {
        const speed = 10;
        const sprintMultiplier = this.keys['ShiftLeft'] ? 1.5 : 1.0;

        // Calculer la direction de mouvement relative à la caméra
        const direction = new THREE.Vector3();

        if (this.keys['KeyW']) {
            direction.z -= 1;
        }
        if (this.keys['KeyS']) {
            direction.z += 1;
        }
        if (this.keys['KeyA']) {
            direction.x -= 1;
        }
        if (this.keys['KeyD']) {
            direction.x += 1;
        }

        // Normaliser et appliquer le mouvement via la vélocité
        if (direction.length() > 0) {
            direction.normalize();

            // Isoler la rotation horizontale seulement
            const horizontalRotation = new THREE.Quaternion();
            horizontalRotation.setFromEuler(new THREE.Euler(0, this.eulerY, 0, 'YXZ'));

            // Appliquer la rotation horizontale à la direction
            direction.applyQuaternion(horizontalRotation);

            // Appliquer le mouvement à la vélocité au lieu de la position directement
            const moveSpeed = speed * sprintMultiplier;
            this.player.velocity.x = direction.x * moveSpeed;
            this.player.velocity.z = direction.z * moveSpeed;
        } else {
            // Arrêter le mouvement horizontal quand aucune touche n'est pressée
            this.player.velocity.x = 0;
            this.player.velocity.z = 0;
        }

        // Saut ou montée en mode vol avec contrôle anti-spam ROBUSTE
        if (this.keys['Space']) {
            const currentTime = Date.now();

            if (this.player.flyMode) {
                // En mode vol, monter
                this.player.velocity.y = 10;
            } else if (this.player.onGround && !this.jumpPressed && (currentTime - this.lastJumpTime) > this.jumpCooldown) {
                // Mode normal, saut avec contrôle anti-spam ROBUSTE
                this.player.velocity.y = 12; // Réduire la force de saut pour éviter les envols
                this.player.onGround = false;
                this.jumpPressed = true;
                this.lastJumpTime = currentTime;

                // Marquer explicitement qu'un saut est en cours
                this.player.isJumping = true;
                this.player.jumpStartTime = currentTime;

                this.logger.physics('Saut effectué', {
                    velocityY: 12,
                    wasOnGround: true,
                    flyMode: false,
                    cooldownRemaining: this.jumpCooldown,
                    jumpStartTime: currentTime
                });
            }
        } else {
            // Réinitialiser le flag de saut quand la touche est relâchée
            this.jumpPressed = false;
        }

        // Descente en mode vol
        if (this.keys['ShiftLeft'] && this.player.flyMode) {
            this.player.velocity.y = -10;
        }

        // Toggle mode vol avec F
        if (this.keys['KeyF'] && !this.flyKeyPressed) {
            this.player.flyMode = !this.player.flyMode;
            this.flyKeyPressed = true;

            if (this.player.flyMode) {
                this.player.velocity.y = 0; // Arrêter la chute
                this.logger.info('Mode vol activé', {
                    playerPosition: {
                        x: Math.round(this.player.camera.position.x * 100) / 100,
                        y: Math.round(this.player.camera.position.y * 100) / 100,
                        z: Math.round(this.player.camera.position.z * 100) / 100
                    },
                    velocityY: this.player.velocity.y
                });
            } else {
                this.logger.info('Mode vol désactivé', {
                    playerPosition: {
                        x: Math.round(this.player.camera.position.x * 100) / 100,
                        y: Math.round(this.player.camera.position.y * 100) / 100,
                        z: Math.round(this.player.camera.position.z * 100) / 100
                    }
                });
            }
        }

        // Gérer le relâchement de la touche F
        if (!this.keys['KeyF']) {
            this.flyKeyPressed = false;
        }

        // Système de minage avec clic gauche
        if (this.isPointerLocked) {
            // Le minage sera géré par les événements de souris
        }

        // Toggle inventaire avec E
        if (this.keys['KeyE'] && !this.inventoryKeyPressed) {
            this.player.toggleInventory();
            this.inventoryKeyPressed = true;
        }

        if (!this.keys['KeyE']) {
            this.inventoryKeyPressed = false;
        }
    }
} 