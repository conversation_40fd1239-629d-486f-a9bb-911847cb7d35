<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Minecraft JS</title>
    <link rel="stylesheet" href="css/style.css" id="main-css">
    <link rel="stylesheet" href="css/mining-ui.css" id="mining-css">
</head>
<body>
    <canvas id="game"></canvas>
    <div id="ui">
        <div id="crosshair">+</div>
        <div id="instructions">Cliquez sur l'écran pour activer la souris | WASD: Déplacer | Espace: Sauter | Clic gauche: Miner | E: Inventaire | F: Mode vol | F1: Options | Échap: Sortir</div>
        <div id="commands">
            <h3>Commandes:</h3>
            <p><strong>WASD</strong> - Se déplacer</p>
            <p><strong>Souris</strong> - Regarder autour</p>
            <p><strong>Clic gauche</strong> - Mine<PERSON> des blocs</p>
            <p><strong>E</strong> - Ouvrir/Fermer l'inventaire</p>
            <p><strong>Espace</strong> - Sauter / Monter (mode vol)</p>
            <p><strong>Shift</strong> - Descendre (mode vol)</p>
            <p><strong>F</strong> - Activer/Désactiver le mode vol</p>
            <p><strong>F1</strong> - Ouvrir le menu des options</p>
            <p><strong>Shift+WASD</strong> - Courir</p>
            <p><strong>F9</strong> - Spawn sur surface (urgence)</p>
            <p><strong>F10</strong> - Réactiver physique</p>
            <p><strong>F11</strong> - Télécharger logs</p>
            <p><strong>Échap</strong> - Libérer la souris</p>
        </div>
        <div id="status">
            <div id="fly-mode">Mode Vol: <span>Désactivé</span></div>
            <div id="position">Position: <span>X: 0, Y: 0, Z: 0</span></div>
            <div id="biome">Biome: <span>Inconnu</span></div>
            <div id="fps">FPS: <span>0</span></div>
        </div>
        
        <!-- Les boutons d'urgence sont maintenant des raccourcis clavier (voir menu d'aide) -->
        <div id="notifications"></div>
        <div id="inventory"></div>
        
        <!-- MENU D'OPTIONS (F1) -->
        <div id="options-menu" style="display: none;">
            <div class="options-content">
                <h2>⚙️ Options du Jeu</h2>
                
                <div class="options-section">
                    <h3>🎨 Rendu et Couleurs</h3>
                    
                    <div class="option-item">
                        <label for="color-mode">Mode de Couleur:</label>
                        <select id="color-mode">
                            <option value="default">Défaut</option>
                            <option value="vibrant">Vibrant</option>
                            <option value="moody">Sombre</option>
                            <option value="classic">Classique Minecraft</option>
                            <option value="warm">Chaleureux</option>
                            <option value="cool">Froid</option>
                        </select>
                    </div>
                    
                    <div class="option-item">
                        <label for="contrast-slider">Contraste:</label>
                        <input type="range" id="contrast-slider" min="50" max="200" value="100">
                        <span id="contrast-value">100%</span>
                    </div>
                    
                    <div class="option-item">
                        <label for="brightness-slider">Luminosité:</label>
                        <input type="range" id="brightness-slider" min="50" max="200" value="100">
                        <span id="brightness-value">100%</span>
                    </div>
                    
                    <div class="option-item">
                        <label for="saturation-slider">Saturation:</label>
                        <input type="range" id="saturation-slider" min="0" max="200" value="100">
                        <span id="saturation-value">100%</span>
                    </div>
                    
                    <div class="option-item">
                        <label for="gamma-slider">Gamma:</label>
                        <input type="range" id="gamma-slider" min="50" max="200" value="100">
                        <span id="gamma-value">1.0</span>
                    </div>
                    
                    <div class="option-item">
                        <input type="checkbox" id="hdr-mode">
                        <label for="hdr-mode">Mode HDR Simplifié (Couleurs Éclatantes)</label>
                    </div>
                    
                    <div class="option-item">
                        <input type="checkbox" id="color-correction">
                        <label for="color-correction">Correction des Couleurs</label>
                    </div>
                </div>
                
                <div class="options-section">
                    <h3>🎮 Gameplay</h3>
                    
                    <div class="option-item">
                        <label for="mouse-sensitivity">Sensibilité Souris:</label>
                        <input type="range" id="mouse-sensitivity" min="0.1" max="5.0" step="0.1" value="2.0">
                        <span id="sensitivity-value">2.0</span>
                    </div>
                    
                    <div class="option-item">
                        <label for="fov-slider">Champ de Vision (FOV):</label>
                        <input type="range" id="fov-slider" min="60" max="120" value="75">
                        <span id="fov-value">75°</span>
                    </div>
                    
                    <div class="option-item">
                        <input type="checkbox" id="auto-climb" checked>
                        <label for="auto-climb">Escalade Automatique (Terrain)</label>
                    </div>
                    
                    <div class="option-item">
                        <input type="checkbox" id="auto-climb-trees">
                        <label for="auto-climb-trees">Escalade Automatique (Arbres)</label>
                    </div>
                </div>
                
                <div class="options-section">
                    <h3>🔧 Performance</h3>
                    
                    <div class="option-item">
                        <label for="render-distance">Distance de Rendu:</label>
                        <input type="range" id="render-distance" min="2" max="32" value="6">
                        <span id="render-distance-value">6 chunks</span>
                    </div>
                    
                    <div class="option-item">
                        <input type="checkbox" id="vsync-mode">
                        <label for="vsync-mode">V-Sync</label>
                    </div>
                </div>
                
                <div class="options-section">
                    <h3>⌨️ Raccourcis d'Urgence</h3>
                    
                    <div class="option-item">
                        <label for="emergency-spawn-key">Spawn Surface:</label>
                        <input type="text" id="emergency-spawn-key" value="F9" readonly>
                        <span>Téléporte sur la surface</span>
                    </div>
                    
                    <div class="option-item">
                        <label for="reset-physics-key">Reset Physique:</label>
                        <input type="text" id="reset-physics-key" value="F10" readonly>
                        <span>Réactive la physique</span>
                    </div>
                    
                    <div class="option-item">
                        <label for="download-logs-key">Télécharger Logs:</label>
                        <input type="text" id="download-logs-key" value="F11" readonly>
                        <span>Sauvegarde les logs</span>
                    </div>
                </div>
                
                <div class="options-buttons">
                    <button id="reset-options">🔄 Réinitialiser</button>
                    <button id="close-options">✅ Fermer</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/three@0.160.0/build/three.min.js"></script>
    <script>
        // CACHE BUSTER - Force le rechargement des scripts à chaque refresh
        const timestamp = Date.now();
        console.log(`🔄 CACHE BUSTER ACTIVÉ - Timestamp: ${timestamp}`);
        
        // Fonction pour charger un script avec cache-busting
        function loadScriptWithCacheBuster(src, isModule = false) {
            const script = document.createElement('script');
            script.src = `${src}?v=${timestamp}`;
            if (isModule) script.type = 'module';
            document.head.appendChild(script);
            console.log(`📦 Script chargé avec cache-buster: ${script.src}`);
        }
        
        // Fonction pour mettre à jour le CSS avec cache-busting
        function updateCSSWithCacheBuster() {
            const cssLink = document.getElementById('main-css');
            const newHref = `css/style.css?v=${timestamp}`;
            cssLink.href = newHref;
            console.log(`🎨 CSS chargé avec cache-buster: ${newHref}`);
        }
        
        // Charger tous les scripts avec cache-busting
        updateCSSWithCacheBuster();
        loadScriptWithCacheBuster('js/version.js', true); // Charger d'abord le système de versioning
        loadScriptWithCacheBuster('js/utils/SmartLogger.js', true); // Système de logs intelligent
        loadScriptWithCacheBuster('js/utils/SimplexNoise.js');
        loadScriptWithCacheBuster('js/main.js', true);
        
        // Scripts de correction supprimés - utilisation du code natif uniquement
    </script>
</body>
</html> 