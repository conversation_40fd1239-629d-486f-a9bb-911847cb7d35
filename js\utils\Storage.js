// Storage.js - Gestion de la sauvegarde locale
export class Storage {
    static save(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
        } catch (e) {
            console.warn('Erreur de sauvegarde:', e);
        }
    }

    static load(key, defaultValue = null) {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : defaultValue;
        } catch (e) {
            console.warn('Erreur de chargement:', e);
            return defaultValue;
        }
    }

    static clear(key) {
        localStorage.removeItem(key);
    }
} 