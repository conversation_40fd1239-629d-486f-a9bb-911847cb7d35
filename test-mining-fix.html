<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Test - Correction Minage</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
        }
        .error {
            border-left-color: #f44336;
        }
        .warning {
            border-left-color: #ff9800;
        }
        .info {
            border-left-color: #2196F3;
        }
        pre {
            background: #333;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #45a049;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            margin: 5px 0;
        }
        .success { background: #4CAF50; }
        .fail { background: #f44336; }
    </style>
</head>
<body>
    <h1>🔧 Test des Corrections de Minage</h1>
    
    <div class="test-section">
        <h2>📋 Corrections Appliquées - Version 2</h2>
        <ul>
            <li>✅ Correction du téléchargement automatique de logs lors des clics</li>
            <li>✅ Réparation de la méthode startMining() avec paramètre world</li>
            <li>✅ Correction de getTargetBlock() pour utiliser le bon monde</li>
            <li>✅ Amélioration des gestionnaires d'événements de souris</li>
            <li>✅ Séparation des logs de minage avec logger.mining()</li>
            <li>✅ Prévention des conflits entre clics et minage</li>
            <li>🆕 <strong>Réduction de la gravité excessive (de 20 à 12)</strong></li>
            <li>🆕 <strong>Correction de la sensibilité souris hyper-rapide (de 0.002 à 0.0008)</strong></li>
            <li>🆕 <strong>Réduction de la force de saut (de 12 à 8)</strong></li>
            <li>🆕 <strong>Amélioration des limites de rotation verticale</strong></li>
            <li>🆕 <strong>Optimisation du système de spawn (limite à 5 tentatives)</strong></li>
            <li>🆕 <strong>Logs de debug détaillés pour le minage</strong></li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🧪 Tests Automatiques</h2>
        <button onclick="runTests()">Lancer les Tests</button>
        <div id="test-results"></div>
    </div>

    <div class="test-section">
        <h2>📊 Diagnostic en Temps Réel</h2>
        <button onclick="startDiagnostic()">Démarrer le Diagnostic</button>
        <button onclick="stopDiagnostic()">Arrêter</button>
        <div id="diagnostic-output"></div>
    </div>

    <div class="test-section">
        <h2>🎮 Instructions de Test Manuel - Version 2</h2>
        <ol>
            <li><strong>Test de Physique :</strong>
                <ul>
                    <li>Ouvrez le jeu principal (index.html)</li>
                    <li>Observez le spawn - doit être plus rapide (max 5 tentatives)</li>
                    <li>Sautez (Espace) - la gravité doit être plus douce</li>
                    <li>Testez la chute - moins brutale qu'avant</li>
                </ul>
            </li>
            <li><strong>Test de Caméra :</strong>
                <ul>
                    <li>Cliquez pour verrouiller le pointeur</li>
                    <li>Bougez la souris - mouvement plus lent et contrôlé</li>
                    <li>Regardez vers le haut - pas de retournement hyper-rapide</li>
                </ul>
            </li>
            <li><strong>Test de Minage :</strong>
                <ul>
                    <li>Ouvrez la console développeur (F12)</li>
                    <li>Maintenez le clic gauche sur un bloc</li>
                    <li>Vérifiez les logs [MINING] dans la console</li>
                    <li>Vérifiez que la barre de progression apparaît</li>
                    <li>Vérifiez que le bloc disparaît après minage</li>
                    <li>Ouvrez l'inventaire (E) pour voir le bloc miné</li>
                </ul>
            </li>
            <li><strong>Test de Logs :</strong>
                <ul>
                    <li>Vérifiez qu'aucun téléchargement automatique ne se déclenche</li>
                    <li>Observez les logs détaillés dans la console</li>
                </ul>
            </li>
        </ol>
    </div>

    <script>
        let diagnosticInterval;
        
        function runTests() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<h3>Exécution des tests...</h3>';
            
            const tests = [
                {
                    name: 'Vérification Logger.js',
                    test: () => {
                        // Simuler un test du logger
                        return fetch('js/utils/Logger.js')
                            .then(response => response.text())
                            .then(content => {
                                return content.includes('Seulement avant fermeture ou erreur critique');
                            });
                    }
                },
                {
                    name: 'Vérification Player.js',
                    test: () => {
                        return fetch('js/player/Player.js')
                            .then(response => response.text())
                            .then(content => {
                                return content.includes('startMining(world = null)') && 
                                       content.includes('logger.mining');
                            });
                    }
                },
                {
                    name: 'Vérification Controls.js',
                    test: () => {
                        return fetch('js/player/Controls.js')
                            .then(response => response.text())
                            .then(content => {
                                return content.includes('this.player.startMining(window.world)') &&
                                       content.includes('e.preventDefault()');
                            });
                    }
                }
            ];
            
            Promise.all(tests.map(test => 
                test.test().then(result => ({
                    name: test.name,
                    passed: result
                })).catch(error => ({
                    name: test.name,
                    passed: false,
                    error: error.message
                }))
            )).then(results => {
                displayTestResults(results);
            });
        }
        
        function displayTestResults(results) {
            const container = document.getElementById('test-results');
            let html = '<h3>Résultats des Tests:</h3>';
            
            results.forEach(result => {
                const status = result.passed ? 'success' : 'fail';
                const icon = result.passed ? '✅' : '❌';
                html += `<div class="status ${status}">${icon} ${result.name}`;
                if (result.error) {
                    html += ` - Erreur: ${result.error}`;
                }
                html += '</div>';
            });
            
            const passedCount = results.filter(r => r.passed).length;
            html += `<p><strong>Résultat: ${passedCount}/${results.length} tests réussis</strong></p>`;
            
            container.innerHTML = html;
        }
        
        function startDiagnostic() {
            const output = document.getElementById('diagnostic-output');
            output.innerHTML = '<p>🔍 Diagnostic en cours...</p>';
            
            diagnosticInterval = setInterval(() => {
                const timestamp = new Date().toLocaleTimeString();
                const diagnosticData = {
                    timestamp,
                    pointerLocked: !!document.pointerLockElement,
                    activeElement: document.activeElement.tagName,
                    windowWorld: typeof window.world !== 'undefined',
                    gameLogger: typeof window.GameLogger !== 'undefined'
                };
                
                let html = '<h4>État Actuel:</h4><pre>';
                html += JSON.stringify(diagnosticData, null, 2);
                html += '</pre>';
                
                // Vérifier les erreurs potentielles
                const warnings = [];
                if (!diagnosticData.windowWorld) {
                    warnings.push('⚠️ window.world non disponible');
                }
                if (!diagnosticData.gameLogger) {
                    warnings.push('⚠️ GameLogger non disponible');
                }
                
                if (warnings.length > 0) {
                    html += '<h4>Avertissements:</h4><ul>';
                    warnings.forEach(warning => {
                        html += `<li>${warning}</li>`;
                    });
                    html += '</ul>';
                }
                
                output.innerHTML = html;
            }, 1000);
        }
        
        function stopDiagnostic() {
            if (diagnosticInterval) {
                clearInterval(diagnosticInterval);
                diagnosticInterval = null;
                document.getElementById('diagnostic-output').innerHTML = '<p>Diagnostic arrêté.</p>';
            }
        }
        
        // Auto-test au chargement
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('🧪 Page de test chargée - Prêt pour les tests');
            }, 500);
        });
    </script>
</body>
</html>
