// ANALYSE DES LOGS - Vérification que les problèmes sont résolus
console.log('📊 ANALYSE DES LOGS - Démarrage...');

class LogsAnalysis {
    constructor() {
        this.analysisResults = {
            jumpSpamResolved: false,
            physicsStable: false,
            noInfiniteLoop: false,
            performanceGood: false,
            spawnSuccessful: false
        };
        
        this.startAnalysis();
    }
    
    async startAnalysis() {
        console.log('🔍 Analyse des logs en cours...');
        
        try {
            // Analyser le fichier de logs
            const response = await fetch('erreurs.txt');
            const logsText = await response.text();
            const logLines = logsText.split('\n');
            
            this.analyzeJumpBehavior(logLines);
            this.analyzePhysicsStability(logLines);
            this.analyzePerformance(logLines);
            this.analyzeSpawnProcess(logLines);
            
            this.generateAnalysisReport();
            
        } catch (error) {
            console.error('❌ Erreur lors de l\'analyse des logs:', error);
        }
    }
    
    analyzeJumpBehavior(logLines) {
        console.log('🦘 Analyse du comportement de saut...');
        
        const jumpLogs = logLines.filter(line => line.includes('[PHYSICS] Saut effectué'));
        const recentJumps = jumpLogs.slice(-10); // 10 derniers sauts
        
        console.log(`  - Nombre total de sauts détectés: ${jumpLogs.length}`);
        console.log(`  - Sauts récents: ${recentJumps.length}`);
        
        if (recentJumps.length > 0) {
            // Analyser les sauts récents
            let spamDetected = false;
            let timestamps = [];
            
            recentJumps.forEach(jumpLog => {
                // Extraire le timestamp du log
                const match = jumpLog.match(/timestamp: (\d+)/);
                if (match) {
                    timestamps.push(parseInt(match[1]));
                }
            });
            
            // Vérifier s'il y a du spam (plus de 5 sauts en 1 seconde)
            for (let i = 1; i < timestamps.length; i++) {
                const timeDiff = timestamps[i] - timestamps[i-1];
                if (timeDiff < 200) { // Moins de 200ms entre sauts
                    spamDetected = true;
                    break;
                }
            }
            
            this.analysisResults.jumpSpamResolved = !spamDetected;
            
            // Vérifier la force de saut (doit être 12 maintenant)
            const lastJump = recentJumps[recentJumps.length - 1];
            const velocityMatch = lastJump.match(/velocityY: (\d+)/);
            if (velocityMatch && parseInt(velocityMatch[1]) === 12) {
                console.log('  ✅ Force de saut corrigée (12 unités)');
            }
            
            console.log(`  - Spam de saut détecté: ${spamDetected ? '❌ OUI' : '✅ NON'}`);
        } else {
            console.log('  ⚠️ Aucun saut récent détecté');
        }
    }
    
    analyzePhysicsStability(logLines) {
        console.log('⚡ Analyse de la stabilité physique...');
        
        const physicsLogs = logLines.filter(line => line.includes('[PHYSICS]'));
        const positioningAttempts = logLines.filter(line => line.includes('Début du positionnement initial'));
        
        console.log(`  - Logs de physique total: ${physicsLogs.length}`);
        console.log(`  - Tentatives de positionnement: ${positioningAttempts.length}`);
        
        // Vérifier s'il y a une boucle infinie de positionnement
        const recentPositioning = positioningAttempts.slice(-20);
        let infiniteLoopDetected = false;
        
        if (recentPositioning.length > 10) {
            // Plus de 10 tentatives récentes = problème potentiel
            const timestamps = recentPositioning.map(log => {
                const match = log.match(/timestamp: (\d+)/);
                return match ? parseInt(match[1]) : 0;
            });
            
            // Vérifier si les tentatives sont très rapprochées
            let rapidAttempts = 0;
            for (let i = 1; i < timestamps.length; i++) {
                if (timestamps[i] - timestamps[i-1] < 50) { // Moins de 50ms
                    rapidAttempts++;
                }
            }
            
            infiniteLoopDetected = rapidAttempts > 5;
        }
        
        this.analysisResults.noInfiniteLoop = !infiniteLoopDetected;
        this.analysisResults.physicsStable = this.analysisResults.noInfiniteLoop;
        
        console.log(`  - Boucle infinie détectée: ${infiniteLoopDetected ? '❌ OUI' : '✅ NON'}`);
    }
    
    analyzePerformance(logLines) {
        console.log('📊 Analyse des performances...');
        
        const fpsLogs = logLines.filter(line => line.includes('fps:'));
        const recentFps = fpsLogs.slice(-5);
        
        if (recentFps.length > 0) {
            const fpsValues = recentFps.map(log => {
                const match = log.match(/fps: (\d+)/);
                return match ? parseInt(match[1]) : 0;
            });
            
            const avgFps = fpsValues.reduce((sum, fps) => sum + fps, 0) / fpsValues.length;
            this.analysisResults.performanceGood = avgFps >= 50;
            
            console.log(`  - FPS moyen récent: ${Math.round(avgFps)}`);
            console.log(`  - Performance: ${avgFps >= 50 ? '✅ BONNE' : '❌ DÉGRADÉE'}`);
        } else {
            console.log('  ⚠️ Aucune donnée FPS récente');
        }
    }
    
    analyzeSpawnProcess(logLines) {
        console.log('🎯 Analyse du processus de spawn...');
        
        const spawnSuccess = logLines.some(line => line.includes('Spawn réussi') || line.includes('Positionnement initial réussi'));
        const spawnFailures = logLines.filter(line => line.includes('Positionnement initial échoué'));
        
        this.analysisResults.spawnSuccessful = spawnSuccess;
        
        console.log(`  - Spawn réussi: ${spawnSuccess ? '✅ OUI' : '❌ NON'}`);
        console.log(`  - Échecs de spawn: ${spawnFailures.length}`);
        
        if (spawnSuccess) {
            const successLog = logLines.find(line => line.includes('Spawn réussi'));
            if (successLog) {
                const groundMatch = successLog.match(/groundHeight: (\d+)/);
                if (groundMatch) {
                    console.log(`  - Hauteur du sol au spawn: ${groundMatch[1]}`);
                }
            }
        }
    }
    
    generateAnalysisReport() {
        console.log('\n📋 RAPPORT D\'ANALYSE DES LOGS');
        console.log('=====================================');
        
        const totalTests = Object.keys(this.analysisResults).length;
        const passedTests = Object.values(this.analysisResults).filter(result => result).length;
        const successRate = Math.round((passedTests / totalTests) * 100);
        
        console.log(`✅ Tests réussis: ${passedTests}/${totalTests} (${successRate}%)`);
        console.log('\nDétail des résultats:');
        
        const testDescriptions = {
            jumpSpamResolved: 'Spam de saut résolu',
            physicsStable: 'Physique stable',
            noInfiniteLoop: 'Pas de boucle infinie',
            performanceGood: 'Performance correcte',
            spawnSuccessful: 'Spawn réussi'
        };
        
        Object.entries(this.analysisResults).forEach(([test, result]) => {
            const status = result ? '✅ SUCCÈS' : '❌ ÉCHEC';
            const description = testDescriptions[test] || test;
            console.log(`  ${status}: ${description}`);
        });
        
        if (successRate >= 80) {
            console.log('\n🎉 LES CORRECTIONS SONT EFFICACES!');
            console.log('Les logs montrent que les problèmes principaux sont résolus.');
        } else {
            console.log('\n⚠️ CERTAINS PROBLÈMES PERSISTENT');
            console.log('Vérifiez les échecs ci-dessus.');
        }
        
        // Recommandations basées sur l'analyse
        this.generateRecommendations();
        
        // Rendre le rapport disponible globalement
        window.logsAnalysisReport = {
            results: this.analysisResults,
            successRate: successRate,
            timestamp: new Date().toISOString()
        };
        
        console.log('\n💡 Rapport disponible dans: window.logsAnalysisReport');
    }
    
    generateRecommendations() {
        console.log('\n🔧 RECOMMANDATIONS:');
        
        if (!this.analysisResults.jumpSpamResolved) {
            console.log('  - Vérifier le système de cooldown de saut');
        }
        
        if (!this.analysisResults.physicsStable) {
            console.log('  - Analyser la logique de physique pour les boucles');
        }
        
        if (!this.analysisResults.performanceGood) {
            console.log('  - Optimiser les performances (viser 60 FPS)');
        }
        
        if (!this.analysisResults.spawnSuccessful) {
            console.log('  - Vérifier la génération de chunks au spawn');
        }
        
        if (Object.values(this.analysisResults).every(result => result)) {
            console.log('  ✅ Aucune action requise - Tout fonctionne correctement!');
        }
    }
}

// Fonction globale pour lancer l'analyse
window.analyzeGameLogs = () => {
    return new LogsAnalysis();
};

// Lancer automatiquement l'analyse
window.addEventListener('load', () => {
    setTimeout(() => {
        console.log('🚀 Lancement automatique de l\'analyse des logs...');
        window.analyzeGameLogs();
    }, 1000);
});

console.log('📊 Analyseur de logs prêt. Utilisez analyzeGameLogs() pour une analyse manuelle.');
